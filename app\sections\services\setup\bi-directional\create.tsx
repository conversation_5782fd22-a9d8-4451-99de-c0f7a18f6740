/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/display-name */
import { Button, Stack, Typography, TextField, Alert, Select, MenuItem, DialogContent, DialogTitle, DialogActions, FormControl, InputLabel, Grid } from "@mui/material"
import _ from 'lodash';
import useMediaQuery from '@mui/material/useMediaQuery';

import { useGetOrganization } from "hooks/api/organization/useGetOrganization"

import { useTable } from "hooks/table/useTable"
import { useEffect, useMemo, useRef, useState, useCallback } from "react"
import useUserDetails from "store/user"

import { useForm } from 'react-hook-form';
import { Form, FormField, FormItem } from "components/@extended/Form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import SimpleBar from "components/third-party/SimpleBar"
import { useServiceProfile } from "hooks/useServiceProfile"
import Table from "components/@extended/Table"
import { DialogClose, Dialog } from "components/@extended/dialog"

const watchFormScheme = z.object({
   type: z
      .string().nonempty("Please enter category."),
   url: z
      .string()
      .nonempty("Please enter url.")
      .url("Please provide a valid URL."),
   // contentType: z.string().max(160).min(4),
   secret: z.string().optional(),
   apiKey: z.string().optional(),
})

type WatchFormValues = z.infer<typeof watchFormScheme>

const contentTypeOptions = [
   { value: 'application/json', label: 'application/json' },
];

const defaultValues: Partial<WatchFormValues> = {
   type: '',
   url: '',
   // contentType: 'application/json',
   secret: '',
   apiKey: ''
}
const OUTSIDE_KEYS = ['secret', 'apiKey']

export default ({
   isOpen,
   onClose: onCloseProp,
   selected,
   isEditMode,
   options,
   preSelectedType,
}: Record<string, any>) => {

   const { user } = useUserDetails(),
      {
         attemptToCreateWatch,
         attemptToUpdateWatch,
      } = useGetOrganization({ id: user?.organization?.id });

   const form = useForm<WatchFormValues>({
      resolver: zodResolver(watchFormScheme),
      defaultValues,
      mode: "onChange",
   })

   const formRef = useRef<HTMLFormElement>(null);

   const onClose = () => {
      onCloseProp && onCloseProp();
      form.reset({})
   }

   // const onSubmit = async (data: any) => {
   //    if (!isEditMode) {
   //       attemptToCreateWatch(data, () => {
   //          onClose()
   //       });
   //    } else {
   //       const payload = Object.entries(form.formState.dirtyFields).reduce((acc: any, [key]) => {
   //          acc.push({
   //             op: "replace",
   //             path: OUTSIDE_KEYS.includes(key) ? `/${key}` : `/data/${key}`,
   //             value: data[key]
   //          })
   //          return acc;
   //       }, [])
   //       attemptToUpdateWatch(payload, selected?.id, () => {
   //          onClose()
   //       });
   //    }
   // };

   const onSubmit = async (data: WatchFormValues) => {
      const { type, url, secret, apiKey } = data;

      const payload = {
         type,
         data: {
            url,
            contentType: "application/json",
            securedSSLRequired: true
         },
         ...(secret && { secret }),
         ...(apiKey && { apiKey })
      };

      if (!isEditMode) {
         attemptToCreateWatch(payload, () => {
            onClose();
         });
      } else {
         const patchPayload = Object.entries(form.formState.dirtyFields).reduce((acc: any, [key]) => {
            acc.push({
               op: "replace",
               path: OUTSIDE_KEYS.includes(key) ? `/${key}` : `/data/${key}`,
               value: data[key as keyof WatchFormValues]
            });
            return acc;
         }, []);
         attemptToUpdateWatch(patchPayload, selected?.id, () => {
            onClose();
         });
      }
   };

   useEffect(() => {
      console.log(preSelectedType)
      if (isEditMode) {
         form.reset({
            // contentType: selected?.data?.contentType,
            type: selected?.type,
            url: selected?.data?.url,
            secret: '',
            apiKey: ''
         })
      } else {
         form.reset({ ...defaultValues, type: preSelectedType || defaultValues?.type })
      }

   }, [selected?.id, isEditMode, isOpen, preSelectedType])

   return (

      <Dialog
         open={isOpen}
         onClose={onClose}
      >
         <DialogTitle variant="h5">
            {!isEditMode ? 'Create' : 'Update'} Webhook
         </DialogTitle>
         <DialogClose onClose={onClose} />

         <DialogContent dividers tabIndex={-1}>

            <SimpleBar sx={{ '& .simplebar-content': { display: 'flex', flexDirection: 'column' } }}>
               <Stack gap={2}>
                  <Alert
                     severity='info'
                  >
                     We'll send a POST request to the URL below with details of any subscribed webhooks. You can also specify which data format you'd like to receive (application/json, etc). More information can be found in our developer documentation.
                  </Alert>
                  <Form {...form}>
                     <Stack
                        component={'form'}
                        onSubmit={(...args) => (
                           void form.handleSubmit(onSubmit)(...args)
                        )}
                        ref={formRef}
                        gap={3}
                     >
                        <FormField
                           control={form.control}
                           name='type'
                           render={({ field }) => (
                              <FormItem label='Select Category' description='Select the Category that best suits your needs.​​'>
                                 <FormControl>
                                    <InputLabel id="select-label">Category</InputLabel>
                                    <Select
                                       disabled={isEditMode || preSelectedType}
                                       labelId="select-label"
                                       {...(
                                          isEditMode ? {
                                             renderValue: () => (
                                                selected?.name
                                             )
                                          } : {}
                                       )}
                                       {...(
                                          preSelectedType ? {
                                             renderValue: () => (
                                                preSelectedType
                                             )
                                          } : {}
                                       )}
                                       {...field}
                                    >
                                       {options?.map((domain: any) => (
                                          <MenuItem value={domain?.value} key={domain?.value}  >{domain?.label}</MenuItem>
                                       ))}
                                    </Select>
                                 </FormControl>
                              </FormItem>
                           )}
                        />
                        <FormField
                           control={form.control}
                           name='url'
                           render={({ field }) => (
                              <FormItem label='Callback Url' description='Enter the Callback Url​.​'>
                                 <FormControl>
                                    <TextField placeholder='https://example.com' {...field} />
                                 </FormControl>
                              </FormItem>
                           )}
                        />
                        {/* <FormField
                           control={form.control}
                           name='contentType'
                           render={({ field }) => (
                              <FormItem label='Content type' description={`Specify the Content Type. For example: 'application/json', 'text/plain', or 'multipart/form-data​.​`}>
                                 <FormControl>
                                    <Select label="Category" {...field} >
                                       {contentTypeOptions.map((domain) => (
                                          <MenuItem value={domain?.value} key={domain?.value}  >{domain?.label}</MenuItem>
                                       ))}
                                    </Select>
                                 </FormControl>
                              </FormItem>
                           )}
                        /> */}
                        <FormField
                           control={form.control}
                           name='secret'
                           render={({ field }) => (
                              <FormItem label='Secret' description='Encrypt your data securely using a secret key.​​​​'>
                                 <FormControl>
                                    <TextField placeholder='********' {...field} />
                                 </FormControl>
                              </FormItem>
                           )}
                        />
                        <FormField
                           control={form.control}
                           name='apiKey'
                           render={({ field }) => (
                              <FormItem label='API Key' description='Enter the API key for the callback Url.​​​'>
                                 <FormControl>
                                    <TextField placeholder='********' {...field} />
                                 </FormControl>
                              </FormItem>
                           )}
                        />
                     </Stack>
                  </Form>
               </Stack>
            </SimpleBar>

         </DialogContent>

         <DialogActions>
            <Stack direction={'row'} justifyContent={'flex-end'} gap={1}>
               <Button onClick={onClose} >Cancel</Button>
               <Button
                  variant='contained'
                  color='primary'
                  // type="submit"
                  onClick={() => {
                     console.log(formRef.current?.requestSubmit())
                  }}
               >Submit</Button>
            </Stack>
         </DialogActions>
      </Dialog >

   )
}