import React, { useState } from "react";
import {
  <PERSON>,
  Tabs,
  Tab,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Chip,
  IconButton,
  Tooltip,
  useTheme,
  alpha,
} from "@mui/material";
import { SettingOutlined, BulbOutlined } from "@ant-design/icons";

import AdditionalFields from "./AdditionalFields";
import FieldAnalytics from "./FieldAnalytics";

interface CategoryFieldManagerProps {
  category: string;
  categoryLabel: string;
  fieldCount?: number;
  onClose?: () => void;
}

export default function CategoryFieldManager({
  category,
  categoryLabel,
}: CategoryFieldManagerProps) {
  const [activeTab, setActiveTab] = useState(0);
  const theme = useTheme();

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const tabItems = [
    {
      label: "Additional Fields",
      icon: <SettingOutlined />,
      description: "Manage additional fields for this category",
      disabled: false,
    },
    {
      label: "Field Analytics",
      icon: <BulbOutlined />,
      description: "Usage insights and recommendations",
      disabled: true,
    },
  ];

  return (
    <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      {/* Header */}
      <Box
        sx={{
          p: 3,
          borderBottom: `1px solid ${theme.palette.mode === "dark" ? "rgba(255, 255, 255, 0.12)" : "#e5e5e5"}`,
        }}
      >
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
        >
          <Stack spacing={1}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Typography variant="h5" fontWeight={600}>
                Configure Fields
              </Typography>
              <Chip
                label={categoryLabel}
                color="primary"
                variant="outlined"
                size="small"
              />
            </Stack>
            <Typography variant="body2" color="text.secondary">
              Configure and manage additional fields at the category level for
              all {categoryLabel} integrations
            </Typography>
          </Stack>
        </Stack>
      </Box>

      {/* Enhanced Tabs */}
      <Box
        sx={{
          borderBottom: `1px solid ${theme.palette.mode === "dark" ? "rgba(255, 255, 255, 0.12)" : "#e5e5e5"}`,
          bgcolor: "background.paper",
        }}
      >
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            "& .MuiTab-root": {
              minHeight: 64,
              textTransform: "none",
              fontWeight: 500,
            },
          }}
        >
          {tabItems.map((item, index) => (
            <Tab
              key={index}
              disabled={item.disabled}
              sx={{
                opacity: item.disabled ? 0.5 : 1,
                pointerEvents: item.disabled ? "none" : "auto",
              }}
              label={
                <Stack direction="row" alignItems="center" spacing={1.5}>
                  {item.icon}
                  <Stack alignItems="flex-start" spacing={0.5}>
                    <Typography variant="body2">{item.label}</Typography>
                    {activeTab === index && (
                      <Typography variant="caption" color="text.secondary">
                        {item.description}
                      </Typography>
                    )}
                  </Stack>
                </Stack>
              }
            />
          ))}
        </Tabs>
      </Box>

      {/* Tab Panels */}
      <Box sx={{ flexGrow: 1, overflow: "hidden" }}>
        {activeTab === 0 && (
          <AdditionalFields category={category} categoryLabel={categoryLabel} />
        )}
        {activeTab === 1 && <FieldAnalytics category={category} />}
      </Box>
    </Box>
  );
}
