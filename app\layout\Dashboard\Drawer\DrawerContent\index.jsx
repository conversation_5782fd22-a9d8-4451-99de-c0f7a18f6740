import useMediaQuery from '@mui/material/useMediaQuery';

// project import
import NavUser from './NavUser';
import NavCard from './NavCard';
import Navigation from './Navigation';
import GetStartedProgressEnhanced from './GetStartedProgressEnhanced';
import SimpleBar from 'components/third-party/SimpleBar';
import { useGetMenuMaster } from 'api/menu';
import useUserDetails from 'store/user';
import { Box, Skeleton, Stack } from '@mui/material';
import { MenuSkeleton } from 'components/@extended/Menu';

// ==============================|| DRAWER CONTENT ||============================== //



export default function DrawerContent() {

  const { isLoading } = useUserDetails();

  const { menuMaster } = useGetMenuMaster();
  const drawerOpen = menuMaster.isDashboardDrawerOpened;
  const downLG = useMediaQuery((theme) => theme.breakpoints.down('lg'));

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <SimpleBar sx={{ 
        flex: 1,
        '& .simplebar-content': { 
          display: 'flex', 
          flexDirection: 'column',
          ...(!drawerOpen && {
            alignItems: 'center'
          })
        } 
      }}>
        {isLoading ? (
          <Stack gap={2}>
            {new Array(2).fill({}).map((_, index) => (
              <MenuSkeleton key={index} />
            ))}
          </Stack>
        ) : (
          <>
            {/* <GetStartedProgressEnhanced drawerOpen={drawerOpen} orgId={window.authUserOrgId} /> */}
            <Navigation />
          </>
        )}
      </SimpleBar>
      {drawerOpen && !downLG && <NavCard />}
    </Box>
  );
}
