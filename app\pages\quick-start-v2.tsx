import { useEffect } from 'react';
import { Box, } from '@mui/material';
import { useSearchParams } from '@remix-run/react';
import { GettingStartedProvider, useGettingStarted } from 'sections/getting-started/context/GettingStartedContext';
import { GettingStartedStepper } from 'sections/getting-started';
import useUserDetails from 'store/user';
import { TenantOnboardType } from 'constants/organization';
import { DOMAINS } from 'data/domains';

const rootStyles = { height: '100vh', display: 'flex', flexDirection: 'column' }
const Content = ({ enabledCategories = [] }: { enabledCategories: string[] }) => {
  const { setPredefinedConfig } = useGettingStarted();

  useEffect(() => {
    setPredefinedConfig(enabledCategories, !!enabledCategories?.length);
  }, [enabledCategories])

  return (
    <Box sx={rootStyles}>
      <GettingStartedStepper />
    </Box>
  )
}

/**
 * Production-ready Quick Start page
 * Uses real subscription data to determine tenant configuration
 */
export default function QuickStartV2() {
  const [params] = useSearchParams()
  const { categories = [] } = useUserDetails();
  return (
    <GettingStartedProvider
      value={{
        onboardingType: params.get('type') as TenantOnboardType,
      }}
    >
      <Content
        enabledCategories={(
          categories.filter(cat => !cat.disabled).map(cat => cat.value)
        )}
        // enabledCategories={DOMAINS.map(cat => cat.value)}
      />
    </GettingStartedProvider>
  );
}