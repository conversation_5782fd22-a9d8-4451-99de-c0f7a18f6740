import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
  Box,
  Grid,
  Typography,
  Button,
  useTheme,
  useMediaQ<PERSON>y,
  Alert,
  AlertTitle,
} from '@mui/material';
import { FileText } from 'lucide-react';
import PageCard from 'components/cards/PageCard';
import CustomTabs, { TabItem } from 'components/getting-started/CustomTabs';
import MobileCustomTabs from 'components/getting-started/MobileCustomTabs';
import EnableConnectorsTab, { Connector } from 'components/getting-started/tabs/EnableConnectorsTab';
import CreateTestConsumerTab from 'components/getting-started/tabs/CreateTestConsumerTab';
import PendingStepTab from 'components/getting-started/tabs/PendingStepTab';
import MakeApiCallTab from 'components/getting-started/tabs/MakeApiCallTab';
import SetupCompleteTab from 'components/getting-started/tabs/SetupCompleteTab';
import { State } from 'hooks/useStatus';
import { serviceProfileClient } from 'services/service-profile.service';
import useUserDetails from 'store/user';
import { AxiosResponse } from 'axios';
import { ResponseModel } from 'types/common';
import { Service } from 'types/service';

import { getQuickStartDomains } from 'data/domains';
import { organizationClient } from 'services/organization.service';
import { Skeleton } from '@mui/material';

// Type mapping for all webhook types (same as in CreateTestConsumerTab)
const typeMap: Record<string, string> = {
  'SCM_WATCH_HOOK': 'Source Code',
  'TICKETING_WATCH_HOOK': 'Ticketing',
  'PCR_WATCH_HOOK': 'Packages & Container registry',
  'COMMS_WATCH_HOOK': 'Communications',
  'INCIDENT_WATCH_HOOK': 'Incident management',
  'VMS_WATCH_HOOK': 'Vulnerability management',
  'KEY_MGMT_WATCH_HOOK': 'Key management',
  'MONITORING_WATCH_HOOK': 'Observability',
  'IDENTITY_WATCH_HOOK': 'Identity',
  'CLOUD_INFRA_WATCH_HOOK': 'Public cloud (Infra)',
  'EDR_XDR_WATCH_HOOK': 'EDR & XDR',
  'SIEM_WATCH_HOOK': 'SIEM',
  'GEN_AI_WATCH_HOOK': 'Gen AI',
  'BLOB_STORAGE_WATCH_HOOK': 'Blob storage',
  'FILE_STORAGE_WATCH_HOOK': 'File storage',
  'PLATFORM_WATCH_HOOK': 'Platform',
};

// Mock webhook data (same as in CreateTestConsumerTab for consistency)
const mockWebhookData = {
  "pagination": {
    "total": 9,
    "offset": 1,
    "previous": 1,
    "next": 1
  },
  "data": [
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/7bab8867-085c-4a9b-8303-7ed59af9aec4",
      "type": "INCIDENT_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "7bab8867-085c-4a9b-8303-7ed59af9aec4",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-03-03T13:14:26.745+00:00",
        "lastUpdatedDateTime": "2025-03-15T11:00:56.011+00:00"
      },
      "data": {
        "url": "https://smee.io/gY5Tvv0DaS7OAkAH",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/77b74738-3770-4c2c-bc46-185a4d95e2b9",
      "type": "PCR_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "77b74738-3770-4c2c-bc46-185a4d95e2b9",
      "eventProfiles": [],
      "organization": {
        "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "type": "STANDARD",
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "name": "The New Snyk, Inc"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-03-19T19:41:03.347+00:00",
        "lastUpdatedDateTime": "2025-03-19T19:41:03.347+00:00"
      },
      "data": {
        "url": "https://smee.io/gY5Tvv0DaS7OAkAH",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/ecd0a902-40c5-4e35-b6ca-bd3809ffa3f3",
      "type": "IDENTITY_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "ecd0a902-40c5-4e35-b6ca-bd3809ffa3f3",
      "eventProfiles": [],
      "organization": {
        "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "type": "STANDARD",
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "name": "The New Snyk, Inc"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-04-02T13:59:14.093+00:00",
        "lastUpdatedDateTime": "2025-04-02T13:59:14.093+00:00"
      },
      "data": {
        "url": "https://mkultrahook-stripe.ultrahook.com",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/287cac77-473d-4b2c-84c0-60a0eef986c8",
      "type": "PLATFORM_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "287cac77-473d-4b2c-84c0-60a0eef986c8",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-02-16T18:59:40.323+00:00",
        "lastUpdatedDateTime": "2025-05-13T05:57:38.264+00:00"
      },
      "data": {
        "url": "https://smee.io/JMCiKcJFaerGtIMR",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/6a79ef73-de90-49a7-91d6-6ed2096da068",
      "type": "COMMS_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "6a79ef73-de90-49a7-91d6-6ed2096da068",
      "eventProfiles": [],
      "organization": {
        "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "type": "STANDARD",
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "name": "The New Snyk, Inc"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-07-09T08:18:18.134+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:27:24.567+00:00"
      },
      "data": {
        "url": "https://smee.io/oCK7nd5eO9mAcYF ",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/45813953-2ab7-4925-8317-b2acb4e6bc7f",
      "type": "VMS_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "45813953-2ab7-4925-8317-b2acb4e6bc7f",
      "eventProfiles": [],
      "organization": {
        "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "type": "STANDARD",
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "name": "The New Snyk, Inc"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-08-06T10:27:56.559+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:29:12.165+00:00"
      },
      "data": {
        "url": "https://kcdgs",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/6505cb07-bb55-4e9b-9a2e-7ffa32eace23",
      "type": "MONITORING_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "6505cb07-bb55-4e9b-9a2e-7ffa32eace23",
      "eventProfiles": [],
      "organization": {
        "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "type": "STANDARD",
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "name": "The New Snyk, Inc"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-08-06T10:33:47.335+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:33:47.335+00:00"
      },
      "data": {
        "url": "https://kcdgs",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/44534313-15e2-47a6-a3bb-fe99e7b21842",
      "type": "SCM_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "44534313-15e2-47a6-a3bb-fe99e7b21842",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T08:18:10.154+00:00",
        "lastUpdatedDateTime": "2025-08-13T11:55:29.335+00:00"
      },
      "data": {
        "url": "https://mkultrahook-stripe.ultrahook.co",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/7ab85511-23c9-421e-a89a-8853fb034f40",
      "type": "TICKETING_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "7ab85511-23c9-421e-a89a-8853fb034f40",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://mktestevent-stripe.ultrahook.com",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc1",
      "type": "KEY_MGMT_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc1",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/keymgmt",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc2",
      "type": "CLOUD_INFRA_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc2",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/cloudinfra",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc3",
      "type": "EDR_XDR_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc3",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/edrxdr",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc4",
      "type": "SIEM_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc4",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/siem",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc5",
      "type": "GEN_AI_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc5",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/genai",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc6",
      "type": "BLOB_STORAGE_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc6",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/blob",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc7",
      "type": "FILE_STORAGE_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc7",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/file",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    }
  ]
};

export default function GettingStartedDashboard() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [activeTab, setActiveTab] = useState(0);
  const [connectorData, setConnectorData] = useState<Connector[]>([]);
  const [connectorPagination, setConnectorPagination] = useState({
    total: 10, // In production, this comes from API
    limit: 4,
    offset: 0
  });

  // Track completion status for sections
  const [webhookConfigured, setWebhookConfigured] = useState(false);
  const [apiCallMade, setApiCallMade] = useState(false);
  const [sourceCodeWebhookConfigured, setSourceCodeWebhookConfigured] = useState(false);
  const [ticketingWebhookConfigured, setTicketingWebhookConfigured] = useState(true); // Set to true to show configured state
  const [connectUIEnabled, setConnectUIEnabled] = useState(false);
  const [apiKeyGenerated, setApiKeyGenerated] = useState(false);
  const [webhookData, setWebhookData] = useState<any[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [dataLoadStatus, setDataLoadStatus] = useState({
    connectors: { loading: true, error: null as string | null },
    webhooks: { loading: true, error: null as string | null },
    dockProfiles: { loading: true, error: null as string | null }
  });
  
  // Use ref to track if data has been loaded to prevent duplicate calls
  const hasInitializedRef = useRef(false);
  const loadingRef = useRef(false);
  
  // Storage keys - use constants to avoid typos
  const STORAGE_KEYS = {
    API_KEY: 'unizo_api_key',
    API_KEY_TIMESTAMP: 'unizo_api_key_timestamp',
    CONNECT_UI_CONFIG: 'unizo_connect_ui_config',
    CONNECT_UI_TIMESTAMP: 'unizo_connect_ui_timestamp'
  } as const;
  
  // Cache duration - 24 hours for API key validation
  const CACHE_DURATION = 24 * 60 * 60 * 1000;

  const { user } = useUserDetails();
  
  // Debug logging utility - disabled in production
  const debugLog = (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[QuickStart] ${message}`, data);
    }
  };

  // Helper function to check if cached data is still valid
  const isCacheValid = (timestampKey: string): boolean => {
    const timestamp = localStorage.getItem(timestampKey);
    if (!timestamp) return false;
    
    const age = Date.now() - parseInt(timestamp, 10);
    return age < CACHE_DURATION;
  };
  
  // Helper function to handle API errors with proper typing
  const handleApiError = (error: any, context: string): string => {
    debugLog(`API Error in ${context}:`, error);
    
    if (error?.response?.status === 401) {
      return 'Authentication failed. Please log in again.';
    } else if (error?.response?.status === 403) {
      return 'You don\'t have permission to access this resource.';
    } else if (error?.response?.status >= 500) {
      return 'Server error. Please try again later.';
    } else if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
      return 'Request timed out. Please check your connection.';
    } else if (!navigator.onLine) {
      return 'No internet connection. Please check your network.';
    }
    
    return 'An unexpected error occurred. Please try again.';
  };
  
  // Fetch connectors with error handling
  const fetchConnectors = async (): Promise<{
    data: Connector[];
    total: number;
    error: string | null;
  }> => {
    try {
      if (!user?.organization?.id) {
        throw new Error('Organization ID not available');
      }
      
      const response = await serviceProfileClient.searchServices({
        filter: {
          and: [
            {
              property: "/state",
              operator: "=",
              values: [State.ACTIVE, State.IN_PROGRESS],
            },
            {
              property: "/organization/id",
              operator: "=",
              values: [user.organization.id],
            },
          ]
        },
        pagination: { limit: 50, offset: 0 }
      });
      
      const transformedData = response.data?.data?.map((item: Service) => ({
        id: item.serviceProfile.id,
        serviceId: item.id, // Add the actual service ID
        name: item.name,
        icon: item.serviceProfile.image.small,
        type: item.type,
        typeLabel: item.type,
        category: item.type,
        requiresConfiguration: !!item.accessPointsSummary?.requiresAction,
        isConfigured: !item.accessPointsSummary?.requiresAction
      })) || [];
      
      return {
        data: transformedData,
        total: response?.data?.pagination?.total || transformedData.length,
        error: null
      };
    } catch (error) {
      const errorMessage = handleApiError(error, 'fetchConnectors');
      return { data: [], total: 0, error: errorMessage };
    }
  };
  
  // Fetch webhooks with error handling
  const fetchWebhooks = async (): Promise<{
    data: any[];
    error: string | null;
  }> => {
    try {
      if (!user?.organization?.id) {
        throw new Error('Organization ID not available');
      }
      
      const response = await organizationClient.getOrgConfig(user.organization.id);
      return {
        data: response?.data?.data || [],
        error: null
      };
    } catch (error) {
      const errorMessage = handleApiError(error, 'fetchWebhooks');
      return { data: [], error: errorMessage };
    }
  };
  
  // Check Connect UI configuration
  const checkConnectUIConfig = async (): Promise<{
    isEnabled: boolean;
    error: string | null;
  }> => {
    try {
      // First check localStorage cache
      const cachedConfig = localStorage.getItem(STORAGE_KEYS.CONNECT_UI_CONFIG);
      if (cachedConfig && isCacheValid(STORAGE_KEYS.CONNECT_UI_TIMESTAMP)) {
        return { isEnabled: true, error: null };
      }
      
      if (!user?.organization?.id) {
        throw new Error('Organization ID not available');
      }
      
      // Fetch dock profiles to check if Connect UI is configured
      const response = await organizationClient.searchOrganizationWatches({
        filter: {
          and: [
            {
              property: `/organization/id`,
              operator: "=",
              values: [user.organization.id]
            }
          ]
        },
        pagination: { limit: 50, offset: 0 }
      });
      
      const dockProfiles = response?.data?.data || [];
      const isEnabled = dockProfiles.length > 0;
      
      if (isEnabled) {
        localStorage.setItem(STORAGE_KEYS.CONNECT_UI_CONFIG, 'true');
        localStorage.setItem(STORAGE_KEYS.CONNECT_UI_TIMESTAMP, Date.now().toString());
      }
      
      return { isEnabled, error: null };
    } catch (error) {
      const errorMessage = handleApiError(error, 'checkConnectUIConfig');
      return { isEnabled: false, error: errorMessage };
    }
  };
  
  // Main data loading function with parallel execution
  const preloadAllData = React.useCallback(async () => {
    // Prevent concurrent executions
    if (loadingRef.current) {
      return;
    }
    
    try {
      if (!user?.organization?.id) {
        if (debugLog) {
          debugLog('Skipping data load - no organization ID');
        }
        setIsInitialLoading(false);
        return;
      }
    } catch (error) {
      console.error('Error in preloadAllData:', error);
      setIsInitialLoading(false);
      return;
    }
    
    // Set loading flag
    loadingRef.current = true;
    
    if (debugLog) {
      debugLog('Starting parallel data fetch', { orgId: user.organization.id });
    }
    setLoadingError(null);
    setIsInitialLoading(true);
  
    try {
      // Execute all API calls in parallel for better performance
      const [connectorsResult, webhooksResult, connectUIResult] = await Promise.allSettled([
        fetchConnectors(),
        fetchWebhooks(),
        checkConnectUIConfig()
      ]);
      
      // Process connectors result
      if (connectorsResult.status === 'fulfilled') {
        const { data, total, error } = connectorsResult.value;
        if (error) {
          setDataLoadStatus(prev => ({ ...prev, connectors: { loading: false, error } }));
        } else {
          setConnectorData(data);
          setConnectorPagination(prev => ({ ...prev, total }));
          setDataLoadStatus(prev => ({ ...prev, connectors: { loading: false, error: null } }));
        }
      } else {
        setDataLoadStatus(prev => ({ 
          ...prev, 
          connectors: { loading: false, error: 'Failed to load connectors' } 
        }));
      }
      
      // Process webhooks result
      if (webhooksResult.status === 'fulfilled') {
        const { data, error } = webhooksResult.value;
        if (error) {
          setDataLoadStatus(prev => ({ ...prev, webhooks: { loading: false, error } }));
        } else {
          setWebhookData(data);
          setDataLoadStatus(prev => ({ ...prev, webhooks: { loading: false, error: null } }));
        }
      } else {
        setDataLoadStatus(prev => ({ 
          ...prev, 
          webhooks: { loading: false, error: 'Failed to load webhooks' } 
        }));
      }
      
      // Process Connect UI result
      if (connectUIResult.status === 'fulfilled') {
        const { isEnabled, error } = connectUIResult.value;
        if (error) {
          setDataLoadStatus(prev => ({ ...prev, dockProfiles: { loading: false, error } }));
        } else {
          setConnectUIEnabled(isEnabled);
          setDataLoadStatus(prev => ({ ...prev, dockProfiles: { loading: false, error: null } }));
        }
      } else {
        setDataLoadStatus(prev => ({ 
          ...prev, 
          dockProfiles: { loading: false, error: 'Failed to check Connect UI status' } 
        }));
      }
      
      // Check for stored API key with cache validation
      const storedApiKey = localStorage.getItem(STORAGE_KEYS.API_KEY);
      if (storedApiKey && isCacheValid(STORAGE_KEYS.API_KEY_TIMESTAMP)) {
        setApiKeyGenerated(true);
      } else if (storedApiKey) {
        // Key exists but cache expired - you might want to validate with API
        if (debugLog) {
          debugLog('API key cache expired, consider revalidation');
        }
        setApiKeyGenerated(true); // For now, assume it's still valid
      }
      
      // Don't set loading error for non-critical failures
      // Users can still use the app with partial data
      
    } catch (error) {
      console.error('Critical error in preloadAllData:', error);
      setLoadingError('Failed to load initial data. Please refresh the page.');
    } finally {
      setIsInitialLoading(false);
      loadingRef.current = false;
      // Save successful load time
      if (!loadingError) {
        localStorage.setItem('unizo_quickstart_last_load', Date.now().toString());
      }
    }
  }, [user?.organization?.id]); // Removed dataLoadStatus to prevent infinite loop

  // Load data only once on mount when user is available
  useEffect(() => {
    if (user?.organization?.id && !hasInitializedRef.current) {
      hasInitializedRef.current = true;
      try {
        preloadAllData();
      } catch (error) {
        console.error('Error loading initial data:', error);
        setIsInitialLoading(false);
        setLoadingError('Failed to load data. Please refresh the page.');
      }
    }
  }, [user?.organization?.id]); // Remove preloadAllData from deps to prevent loops
  
  // Refresh data when user navigates back to this page
  useEffect(() => {
    const handleFocus = () => {
      // Only refresh if it's been more than 5 minutes since last load and not currently loading
      if (!loadingRef.current) {
        try {
          const lastLoad = localStorage.getItem('unizo_quickstart_last_load');
          if (lastLoad) {
            const timeSinceLastLoad = Date.now() - parseInt(lastLoad, 10);
            if (timeSinceLastLoad > 5 * 60 * 1000) {
              if (debugLog) {
                debugLog('Refreshing data after 5 minutes');
              }
              hasInitializedRef.current = false; // Allow refresh
              preloadAllData();
            }
          }
        } catch (error) {
          console.error('Error checking last load time:', error);
        }
      }
    };
    
    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [preloadAllData, debugLog]);
  
  // Remove the duplicate save last load time effect as it's now handled in preloadAllData
  
  // Check if all webhooks are configured with proper validation
  const allWebhooksConfigured = useMemo(() => {
    try {
      if (!webhookData || webhookData.length === 0) return false;
      
      // Check for platform webhook
      const platformWebhook = webhookData.find(webhook => 
        webhook?.type === 'PLATFORM_WATCH_HOOK' && 
        webhook?.state === 'SUBMITTED'
      );
      
      // Check for at least one category-specific webhook
      const categoryWebhooks = webhookData.filter(webhook => 
        webhook?.type !== 'PLATFORM_WATCH_HOOK' && 
        webhook?.state === 'SUBMITTED' &&
        typeMap[webhook?.type] // Ensure it's a valid webhook type
      );
      
      if (debugLog) {
        debugLog('Webhook configuration status', {
          platformConfigured: !!platformWebhook,
          categoryWebhooksCount: categoryWebhooks.length,
          totalWebhooks: webhookData.length
        });
      }
      
      return !!platformWebhook && categoryWebhooks.length > 0;
    } catch (error) {
      if (debugLog) {
        debugLog('Error checking webhook configuration', error);
      }
      return false;
    }
  }, [webhookData, debugLog]);


  // Create dynamic tabs based on webhook configuration status
  const tabs: TabItem[] = useMemo(() => {
    const hasConnectors = connectorData.length > 0;
    const isIntegrationComplete = apiKeyGenerated && connectUIEnabled;
    const allComplete = hasConnectors && allWebhooksConfigured && isIntegrationComplete;

    return [
      {
        id: 0,
        number: "1",
        title: "Setup Connectors",
        helpText: "Configure connectors for workflows in your product​.",
        status: hasConnectors ? 'completed' : 'active',
      },
      {
        id: 1,
        number: "2",
        title: "Add Webhook Endpoints​",
        helpText: "Tell Unizo where to send events to keep your product updated in real time​",
        status: allWebhooksConfigured ? 'completed' : (hasConnectors ? 'active' : 'pending'),
      },
      {
        id: 2,
        number: "3",
        title: "Connect Unizo to your Product",
        helpText: "Enable secure API access and decide how authentication is handled in your application.",
        status: isIntegrationComplete ? 'completed' : (allWebhooksConfigured ? 'active' : 'pending'),
      },
      {
        id: 3,
        number: "4",
        title: "Setup Complete",
        helpText: "Your product is now ready to build workflow with Unizo.",
        status: allComplete ? 'active' : 'pending',
      }
    ];
  }, [allWebhooksConfigured, connectUIEnabled, apiKeyGenerated, connectorData.length]);


  const handleActionClick = () => {
    // Handle action button click
    console.log('Action clicked');
    // In a real app, this would be fetched from an API or state management
    setApiCallMade(true);
  };
  
  // TODO: Add webhook data fetching
  // useEffect(() => {
  //   fetchWebhookData();
  // }, [user?.organization?.id]);

  // In a real app, this would be called after webhook is configured
  const handleWebhookConfigured = () => {
    setWebhookConfigured(true);
  };

  const handleLoadMoreConnectors = () => {
    // In production, this would make an API call with pagination params
    // For demo, we'll simulate loading more data
    setTimeout(() => {
      const newOffset = connectorPagination.offset + connectorPagination.limit;
      setConnectorPagination(prev => ({
        ...prev,
        offset: newOffset
      }));
      // In real app, append new data to connectorData
      // setConnectorData(prev => [...prev, ...newData]);
    }, 1000);
  };

  const handleNext = () => {
    if (activeTab < tabs.length - 1) {
      setActiveTab(activeTab + 1);
    }
  };

  const handlePrevious = () => {
    if (activeTab > 0) {
      setActiveTab(activeTab - 1);
    }
  };

  const renderTabContent = () => {
    try {
    const navigationProps = {
      onNext: handleNext,
      onPrevious: handlePrevious,
      isFirstTab: activeTab === 0,
      isLastTab: activeTab === tabs.length - 1,
    };

    switch (activeTab) {
      case 0:
        return (
          <EnableConnectorsTab
            connectors={connectorData}
            pagination={connectorPagination}
            onLoadMore={handleLoadMoreConnectors}
            isLoading={false} // In production, track loading state
            {...navigationProps}
            nextLabel="Continue"
            onRefreshConnectors={async () => {
              debugLog('Refreshing connectors after configuration update');
              const result = await fetchConnectors();
              if (!result.error) {
                setConnectorData(result.data);
                setConnectorPagination(prev => ({ ...prev, total: result.total }));
              }
            }}
          />
        );
      case 1:
        return (
          <CreateTestConsumerTab 
            {...navigationProps}
            nextLabel="Continue"
            previousLabel="Back"
          />
        );
      case 2:
        return (
          <MakeApiCallTab 
            onConnectUIStatusChange={(status) => {
              setConnectUIEnabled(status);
              if (status) {
                localStorage.setItem(STORAGE_KEYS.CONNECT_UI_CONFIG, 'true');
                localStorage.setItem(STORAGE_KEYS.CONNECT_UI_TIMESTAMP, Date.now().toString());
              } else {
                localStorage.removeItem(STORAGE_KEYS.CONNECT_UI_CONFIG);
                localStorage.removeItem(STORAGE_KEYS.CONNECT_UI_TIMESTAMP);
              }
            }}
            onApiKeyGenerated={(hasKey) => {
              setApiKeyGenerated(hasKey);
              if (hasKey) {
                localStorage.setItem(STORAGE_KEYS.API_KEY, 'true');
                localStorage.setItem(STORAGE_KEYS.API_KEY_TIMESTAMP, Date.now().toString());
              } else {
                localStorage.removeItem(STORAGE_KEYS.API_KEY);
                localStorage.removeItem(STORAGE_KEYS.API_KEY_TIMESTAMP);
              }
            }}
            {...navigationProps}
            nextLabel="Continue"
            previousLabel="Back"
          />
        );
      case 3:
        return (
          <SetupCompleteTab
            connectorsEnabled={connectorData.length > 0}
            eventListenersConfigured={allWebhooksConfigured}
            apiKeyGenerated={apiKeyGenerated}
            connectUIEnabled={connectUIEnabled}
            {...navigationProps}
            previousLabel="Back"
          />
        );
      default:
        return null;
    }
    } catch (error) {
      console.error('Error rendering tab content:', error);
      return (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography color="error">
            Error loading content. Please refresh the page.
          </Typography>
        </Box>
      );
    }
  };

  // Show skeleton loader during initial load
  if (isInitialLoading) {
    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Skeleton variant="text" width={200} height={40} />
          <Skeleton variant="text" width={400} height={20} sx={{ mt: 1 }} />
        </Grid>
        <Grid item xs={12}>
          <PageCard sx={{ p: 0 }}>
            <Box sx={{ p: 3 }}>
              <Skeleton variant="rectangular" height={100} sx={{ mb: 3 }} />
              <Grid container spacing={2}>
                {[1, 2, 3, 4].map(i => (
                  <Grid key={i} item xs={12} sm={6} md={3}>
                    <Skeleton variant="rectangular" height={160} />
                  </Grid>
                ))}
              </Grid>
            </Box>
          </PageCard>
        </Grid>
      </Grid>
    );
  }
  
  // Show error state if critical error occurred
  if (loadingError && !isInitialLoading) {
    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Grid container>
            <Grid item xs={12}>
              <Typography variant="h4">Quick Start</Typography>
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12}>
          <PageCard>
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h6" color="error" gutterBottom>
                {loadingError}
              </Typography>
              <Button 
                variant="contained" 
                onClick={() => window.location.reload()}
                sx={{ mt: 2 }}
              >
                Refresh Page
              </Button>
            </Box>
          </PageCard>
        </Grid>
      </Grid>
    );
  }

  return (
    <Grid container spacing={3}>
      {/* Header */}
      <Grid item xs={12}>
        <Grid container>
          <Grid item xs={12}>
            <Typography variant="h4">
              Quick Start
            </Typography>
          </Grid>
          <Grid item xs={12} lg={10}>
            <Typography variant="body1" className="font-normal">
              Follow these steps to quick start building integrations with Unizo.
            </Typography>
          </Grid>
        </Grid>
      </Grid>

      {/* Custom Tabs wrapped in PageCard */}
      <Grid item xs={12}>
        <PageCard sx={{ p: 0 }}>
          <Box sx={{ px: { xs: 2, sm: 3, md: 4 }, pb: { xs: 3, md: 4 }, pt: 0 }}>
            {/* Show non-critical errors as alerts */}
            {!isInitialLoading && Object.values(dataLoadStatus).some(s => s.error) && (
              <Alert severity="warning" sx={{ mb: 2 }}>
                <AlertTitle>Some data could not be loaded</AlertTitle>
                {dataLoadStatus.connectors.error && (
                  <Typography variant="body2">Connectors: {dataLoadStatus.connectors.error}</Typography>
                )}
                {dataLoadStatus.webhooks.error && (
                  <Typography variant="body2">Webhooks: {dataLoadStatus.webhooks.error}</Typography>
                )}
                {dataLoadStatus.dockProfiles.error && (
                  <Typography variant="body2">Connect UI: {dataLoadStatus.dockProfiles.error}</Typography>
                )}
              </Alert>
            )}
            
            {isMobile ? (
              <MobileCustomTabs
                tabs={tabs}
                activeTab={activeTab}
                onTabChange={setActiveTab}
              >
                {renderTabContent()}
              </MobileCustomTabs>
            ) : (
              <CustomTabs
                tabs={tabs}
                activeTab={activeTab}
                onTabChange={setActiveTab}
              >
                {renderTabContent()}
              </CustomTabs>
            )}
          </Box>
        </PageCard>
      </Grid>
    </Grid>
  );
}

