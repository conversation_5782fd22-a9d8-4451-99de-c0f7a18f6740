import fetchInstance from 'utils/api/fetchinstance';
import { API_ENDPOINTS } from 'utils/api/api-endpoints';
import { Webhook, CreateWebhookPayload, UpdateWebhookPayload } from 'types/webhook';

export const webhookClient = {
  getWebhooks: async (organizationId: string): Promise<Webhook[]> => {
    const { data } = await fetchInstance.get(`${API_ENDPOINTS.ORGANIZATION}s/${organizationId}/configurations`);
    return data;
  },

  createWebhook: async (organizationId: string, payload: CreateWebhookPayload): Promise<Webhook> => {
    const { data } = await fetchInstance.post(
      `${API_ENDPOINTS.ORGANIZATION}s/${organizationId}/configurations`,
      payload
    );
    return data;
  },

  updateWebhook: async (
    organizationId: string, 
    webhookId: string, 
    payload: UpdateWebhookPayload
  ): Promise<Webhook> => {
    const { data } = await fetchInstance.patch(
      `${API_ENDPOINTS.ORGANIZATION}s/${organizationId}/configurations/${webhookId}`,
      payload
    );
    return data;
  },

  deleteWebhook: async (organizationId: string, webhookId: string): Promise<void> => {
    await fetchInstance.delete(`${API_ENDPOINTS.ORGANIZATION}s/${organizationId}/configurations/${webhookId}`);
  }
};