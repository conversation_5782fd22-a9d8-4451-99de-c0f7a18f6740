import { CheckOutlined, CopyOutlined } from '@ant-design/icons';
import { Stack, Tooltip } from '@mui/material';
import { CSSProperties, useState } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';

type Props = {
   children: React.ReactElement<any, any>
} & CopyToClipboard.Props;

const TOOLTIP_TEXTS = {
   COPY: 'Copy',
   COPIED: 'Copied',
};

const ICON_STYLE_S: CSSProperties = {
   cursor: 'pointer',
}

export default ({
   children,
   text = '',
   onCopy: onCopyProp,
   ...rest
}: Props) => {

   const [tooltipText, setTooltipText] = useState(TOOLTIP_TEXTS.COPY);
   const [localIsCopied, setLocalIsCopied] = useState(false);


   const onCopy: Props['onCopy'] = (e, option) => {
      if (typeof onCopyProp === 'function') {
         onCopyProp(e, option);
      }
      setTooltipText(TOOLTIP_TEXTS.COPIED);
      setLocalIsCopied(true);

      // revert the tooltip text
      setTimeout(() => {
         setTooltipText(TOOLTIP_TEXTS.COPY);
         setLocalIsCopied(false);
      }, 2000);
   }

   return (
      <CopyToClipboard {...rest} text={text} onCopy={onCopy}>

         <Stack direction={'row'} gap={.6} alignItems={'center'}>
            {children}

            {text && (
               <Tooltip
                  title={tooltipText}
                  placement="right"
               >

                  {localIsCopied ? (
                     <CheckOutlined style={ICON_STYLE_S} />
                  ) : (
                     <CopyOutlined style={ICON_STYLE_S} />
                  )}

               </Tooltip>
            )}

         </Stack>
      </CopyToClipboard>
   )
}