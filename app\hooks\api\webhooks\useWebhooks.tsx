import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { webhookClient } from 'services/webhook.service';
import { CreateWebhookPayload, UpdateWebhookPayload } from 'types/webhook';

export const useWebhooks = () => {
  const queryClient = useQueryClient();

  const useGetWebhooks = (organizationId: string) => {
    return useQuery({
      queryKey: ['webhooks', organizationId],
      queryFn: () => webhookClient.getWebhooks(organizationId),
      enabled: !!organizationId,
      staleTime: 5 * 60 * 1000 // 5 minutes
    });
  };

  const createWebhook = useMutation({
    mutationFn: ({ organizationId, data }: { organizationId: string; data: CreateWebhookPayload }) =>
      webhookClient.createWebhook(organizationId, data),
    onSuccess: (_, { organizationId }) => {
      queryClient.invalidateQueries({ queryKey: ['webhooks', organizationId] });
      toast.success('Webhook created successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create webhook');
    }
  });

  const updateWebhook = useMutation({
    mutationFn: ({ 
      organizationId, 
      webhookId, 
      data 
    }: { 
      organizationId: string; 
      webhookId: string; 
      data: UpdateWebhookPayload 
    }) => webhookClient.updateWebhook(organizationId, webhookId, data),
    onSuccess: (_, { organizationId }) => {
      queryClient.invalidateQueries({ queryKey: ['webhooks', organizationId] });
      toast.success('Webhook updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update webhook');
    }
  });

  const deleteWebhook = useMutation({
    mutationFn: ({ organizationId, webhookId }: { organizationId: string; webhookId: string }) =>
      webhookClient.deleteWebhook(organizationId, webhookId),
    onSuccess: (_, { organizationId }) => {
      queryClient.invalidateQueries({ queryKey: ['webhooks', organizationId] });
      toast.success('Webhook deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete webhook');
    }
  });

  return {
    useGetWebhooks,
    createWebhook,
    updateWebhook,
    deleteWebhook
  };
};