import React, { useState, useEffect, useMemo } from 'react';
import {
  <PERSON>er,
  <PERSON>,
  Typo<PERSON>,
  IconButton,
  Stack,
  Avatar,
  useTheme,
  Divider,
  Card,
  Button,
  Skeleton,
  Alert,
  Chip,
  alpha,
  Link,
  Switch,
  Tooltip,
  TextField,
  CircularProgress,
} from '@mui/material';
import { X, Check, ExternalLink, AlertCircle, Sparkles, ChevronRight, ArrowLeft, Eye, EyeOff } from 'lucide-react';
import { useGetAccessPoint, AccessPointConfigType, ServiceConfigState } from 'hooks/api/use-accesspoint-config/useGetAccessPoint';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';

interface ConnectorAuthenticationDrawerProps {
  open: boolean;
  onClose: () => void;
  connector: {
    id: string;
    name: string;
    icon: string;
    type?: string;
  };
  serviceId: string;
  onRefreshConnectors?: () => void;
}

interface AccessPoint {
  id: string;
  type: AccessPointConfigType;
  label: string;
  recommended?: boolean;
  isBeta?: boolean;
  isEnabled?: boolean;
  accessPoint?: {
    id: string;
    state: ServiceConfigState;
  };
}

// Get the configuration container based on access point type
const getAccessPointTypeContainer = (accessPoint: any) => {
  switch (accessPoint?.type) {
    case AccessPointConfigType.AppFlow:
      return accessPoint?.appConfig?.[0];
    case AccessPointConfigType.OAuthFlow:
      return accessPoint?.oAuthConfig?.[0];
    case AccessPointConfigType.APIKeyFlow:
      return accessPoint?.apiKeyConfig;
    default:
      console.warn(`No Container found for ${accessPoint?.type}`);
      return null;
  }
};

// Define the form schema based on the accessPoint type
const createFormSchema = (segments: any[]) => {
  const schemaObj: Record<string, any> = {};
  
  segments.forEach((segment) => {
    const segmentSchema: Record<string, any> = {};
    
    segment.fieldTypeConfigs?.forEach((config: any) => {
      // Extract property name for schema
      const propertyName = config.property.split('/').pop() || config.property;
      
      if (config.type === 'FILE') {
        // Skip file fields in validation for now
        segmentSchema[propertyName] = z.any().optional();
      } else {
        // Start with base string validation
        let fieldValidation = z.string();
        
        // Add required validation
        if (config.required) {
          fieldValidation = fieldValidation.nonempty(`${config.label} is required`);
        } else {
          fieldValidation = fieldValidation.optional();
        }
        
        // Apply additional validations dynamically
        if (config.validations && Array.isArray(config.validations)) {
          config.validations.forEach((validation: any) => {
            switch (validation.type) {
              case 'regex':
                try {
                  const regexPattern = new RegExp(validation.value);
                  // Fix error message formatting (e.g., "RedirectURlisininvalidURLformat." -> "Redirect URL is in invalid URL format.")
                  const formattedErrorMessage = validation.errorMessage
                    ? validation.errorMessage.replace(/([a-z])([A-Z])/g, '$1 $2').replace(/([A-Z])([A-Z][a-z])/g, '$1 $2')
                    : `${config.label} format is invalid`;
                  
                  fieldValidation = fieldValidation.regex(
                    regexPattern,
                    formattedErrorMessage
                  );
                } catch (e) {
                  console.error('Invalid regex pattern:', validation.value, e);
                }
                break;
              
              // Add more validation types here as needed
              case 'min':
                if (validation.value) {
                  fieldValidation = fieldValidation.min(
                    validation.value, 
                    validation.errorMessage || `${config.label} must be at least ${validation.value} characters`
                  );
                }
                break;
                
              case 'max':
                if (validation.value) {
                  fieldValidation = fieldValidation.max(
                    validation.value, 
                    validation.errorMessage || `${config.label} must be no more than ${validation.value} characters`
                  );
                }
                break;
                
              case 'email':
                fieldValidation = fieldValidation.email(
                  validation.errorMessage || `${config.label} must be a valid email`
                );
                break;
                
              case 'url':
                fieldValidation = fieldValidation.url(
                  validation.errorMessage || `${config.label} must be a valid URL`
                );
                break;
                
              default:
                console.warn(`Unknown validation type: ${validation.type}`);
            }
          });
        }
        
        segmentSchema[propertyName] = fieldValidation;
      }
    });
    
    schemaObj[segment.key] = z.object(segmentSchema);
  });
  
  return z.object(schemaObj);
};

export default function ConnectorAuthenticationDrawer({
  open,
  onClose,
  connector,
  serviceId,
  onRefreshConnectors,
}: ConnectorAuthenticationDrawerProps) {
  const theme = useTheme();
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [updatingMethods, setUpdatingMethods] = useState<Set<string>>(new Set());
  const [selectedAccessPoint, setSelectedAccessPoint] = useState<AccessPoint | null>(null);
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState<Record<string, boolean>>({});

  const {
    serviceProfileAccessPoints,
    isLoading,
    attemptUpdateAccessPointsSelection,
    attemptUpdateAccessPoints,
  } = useGetAccessPoint({
    serviceProfileId: connector.id,
    serviceId: serviceId,
  });

  // Map access point types to user-friendly names and descriptions
  const getAuthMethodInfo = (type: AccessPointConfigType): { name: string; description: string } => {
    const authInfo = {
      [AccessPointConfigType.AppFlow]: {
        name: 'GitHub App',
        description: 'Using GitHub App credentials ensures your logo appears in the pop-up during user authentication, providing a branded experience.',
      },
      [AccessPointConfigType.APIKeyFlow]: {
        name: 'Personal Access Token',
        description: 'Using PAT credentials for authenticating',
      },
      [AccessPointConfigType.OAuthFlow]: {
        name: 'OAuth Flow Token',
        description: 'Using OAuth Flow Token credentials ensures your logo appears in the pop-up during user authentication, providing a branded experience.',
      },
      [AccessPointConfigType.OAuthPasswordFlow]: {
        name: 'OAuth Password Flow',
        description: 'Username and password based OAuth authentication',
      },
    };
    return authInfo[type] || { name: type, description: '' };
  };

  // Sort access points with configured ones first
  const sortedAccessPoints = React.useMemo(() => {
    return [...serviceProfileAccessPoints].sort((a, b) => {
      // Configured items first
      if (a.accessPoint?.state === ServiceConfigState.Configured && b.accessPoint?.state !== ServiceConfigState.Configured) return -1;
      if (b.accessPoint?.state === ServiceConfigState.Configured && a.accessPoint?.state !== ServiceConfigState.Configured) return 1;
      
      // Then needs configuration
      if (a.accessPoint?.state === ServiceConfigState.ReadyToConfig && b.accessPoint?.state !== ServiceConfigState.ReadyToConfig) return -1;
      if (b.accessPoint?.state === ServiceConfigState.ReadyToConfig && a.accessPoint?.state !== ServiceConfigState.ReadyToConfig) return 1;
      
      // Then available
      if ((a.isEnabled !== false && !a.accessPoint) && (b.isEnabled === false || b.accessPoint)) return -1;
      if ((b.isEnabled !== false && !b.accessPoint) && (a.isEnabled === false || a.accessPoint)) return 1;
      
      // Disabled last
      if (a.accessPoint?.state === ServiceConfigState.Disabled && b.accessPoint?.state !== ServiceConfigState.Disabled) return 1;
      if (b.accessPoint?.state === ServiceConfigState.Disabled && a.accessPoint?.state !== ServiceConfigState.Disabled) return -1;
      
      return 0;
    });
  }, [serviceProfileAccessPoints]);

 const handleToggleMethod = async (accessPoint: AccessPoint) => {
  const accessPointId = accessPoint.accessPoint?.id;
  console.log(accessPointId,"accessPointId");

  if (!accessPointId) {
    console.error("No access point ID found");
    return;
  }
  // Add to updating set
  setUpdatingMethods(prev => new Set(prev).add(accessPoint.id));

  // Determine if currently enabled
  const isCurrentlyEnabled =
    accessPoint.accessPoint?.state !== ServiceConfigState.Disabled;

  // Toggle the value
  const newValue = !isCurrentlyEnabled; 

  // Build payload dynamically
  const operations = [
    {
      op: "replace",
      path: "/isEnabled",
      value: newValue,
    },
  ];

  // Create FormData with operations as JSON string
  const payload = new FormData();
  payload.append('operations', JSON.stringify(operations));

  try {
    await attemptUpdateAccessPoints(
      serviceId,
      accessPointId,
      payload,
      () => {
        // Remove from updating set
        setUpdatingMethods(prev => {
          const newSet = new Set(prev);
          newSet.delete(accessPoint.id);
          return newSet;
        });

        // Refresh connectors
        if (onRefreshConnectors) {
          onRefreshConnectors();
        }
      }
    );
  } catch (error) {
    console.error("Failed to toggle:", error);
    setUpdatingMethods(prev => {
      const newSet = new Set(prev);
      newSet.delete(accessPoint.id);
      return newSet;
    });
  }
};


  // Get configuration container and segments
  const configContainer = useMemo(() => {
    if (!selectedAccessPoint) return null;
    return getAccessPointTypeContainer(selectedAccessPoint);
  }, [selectedAccessPoint]);

  const segments = useMemo(() => {
    return configContainer?.segments || [];
  }, [configContainer]);

  // Create form schema
  const formSchema = useMemo(() => {
    if (!segments.length) return z.object({});
    return createFormSchema(segments);
  }, [segments]);

  // Create default values with existing data when editing
  const defaultValues = useMemo(() => {
    const values: Record<string, any> = {};
    
    segments.forEach((segment: any) => {
      values[segment.key] = {};
      segment.fieldTypeConfigs?.forEach((config: any) => {
        // Match the form field name structure used in Controller
        const propertyName = config.property.split('/').pop() || config.property;
        let foundValue: any = '';
        
        // Check if we have existing data (editing mode)
        if (selectedAccessPoint?.accessPoint) {
          const pathParts = config.property.split('/').filter(Boolean);
          
          // Try multiple possible data locations
          const possibleDataSources = [
            selectedAccessPoint.accessPoint.data,
            selectedAccessPoint.accessPoint,
            selectedAccessPoint
          ];
          
          for (const dataSource of possibleDataSources) {
            if (!dataSource || foundValue) continue;
            
            let currentValue = dataSource;
            let isValid = true;
            
            // Navigate through the path
            for (const part of pathParts) {
              if (currentValue && typeof currentValue === 'object' && part in currentValue) {
                currentValue = currentValue[part];
              } else {
                isValid = false;
                break;
              }
            }
            
            if (isValid && currentValue !== undefined && currentValue !== null) {
              foundValue = currentValue;
              break;
            }
          }
        }
        
        // Store the value using propertyName to match Controller name
        values[segment.key][propertyName] = foundValue;
      });
    });
    
    return values;
  }, [segments, selectedAccessPoint]);

  // Initialize form
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitted },
    reset,
    trigger,
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues,
    mode: 'onBlur',
    criteriaMode: 'all',
  });

  // Effect to reset form when configuration view opens (especially for edit mode)
  useEffect(() => {
    if (isConfiguring && selectedAccessPoint) {
      reset(defaultValues);
    }
  }, [isConfiguring, selectedAccessPoint, defaultValues, reset]);

  // Handle opening configuration
  const handleConfigure = (accessPointId: string) => {
    const accessPoint = serviceProfileAccessPoints.find((ap: AccessPoint) => ap.id === accessPointId);
    console.log(accessPoint,"accessPoint")
    if (accessPoint) {
      setSelectedAccessPoint(accessPoint);
      setIsConfiguring(true);
    }
  };

  // Handle back to list
  const handleBackToList = () => {
    setIsConfiguring(false);
    setSelectedAccessPoint(null);
    reset();
    setShowPassword({});
  };

  // Handle form submission
  const onSubmitConfiguration = async (data: any) => {
    if (!selectedAccessPoint) return;
    
    // Trigger validation for all fields
    const isValid = await trigger();
    
    if (!isValid) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    setIsSubmitting(true);
    try {
      // Validate that we have at least some data to submit
      const hasValidData = Object.values(data).some((segmentData: any) => 
        Object.values(segmentData).some((value) => value && value.toString().trim())
      );

      if (!hasValidData) {
        toast.error('Please fill in all required fields');
        setIsSubmitting(false);
        return;
      }

      // First check if we need to create the access point (if it's not configured yet)
      // Check if we need to enable the access point first
      const needsEnabling = !selectedAccessPoint.accessPoint || 
        selectedAccessPoint.accessPoint.state === undefined;

      if (needsEnabling) {
        // First, we need to enable/create the access point
        const enablePayload = {
          data: [{
        op: "replace",
        path: "/description",
        value: true
      }]
        };

        // Enable the access point first
        try {
          await new Promise((resolve, reject) => {
            attemptUpdateAccessPointsSelection(
              serviceId,
              enablePayload,
              (result: any) => {
                resolve(result);
              }
            );
          });

          // Small delay to ensure the access point is created
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          toast.error('Failed to enable authentication method');
          throw error;
        }
      }
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || 'Failed to save configuration';
      toast.error(errorMessage);
      console.error('Configuration error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const AuthMethodCard = ({ accessPoint }: { accessPoint: AccessPoint }) => {
    const isConfigured = accessPoint.accessPoint?.state === ServiceConfigState.Configured;
    const needsConfig = accessPoint.accessPoint?.state === ServiceConfigState.ReadyToConfig;
    const isDisabled = accessPoint.accessPoint?.state === ServiceConfigState.Disabled;
    const isEnabled = isConfigured || needsConfig;
    const isUpdating = updatingMethods.has(accessPoint.id);
    const authInfo = getAuthMethodInfo(accessPoint.type);

    return (
      <Card
        variant="outlined"
        sx={{
          p: 3,
          border: `1px solid ${theme.palette.divider}`,
          backgroundColor: theme.palette.background.paper,
          cursor: 'pointer',
          transition: 'all 0.2s',
          position: 'relative',
          opacity: isDisabled ? 0.6 : 1,
          '&:hover': {
            borderColor: theme.palette.primary.main,
            boxShadow: `0 2px 8px ${alpha(theme.palette.common.black, 0.08)}`,
          },
        }}
        onClick={() => setSelectedMethod(accessPoint.id)}
      >
        {/* Enable/Disable Switch - Only show for configured methods */}
        {isConfigured && (
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <Tooltip title={isEnabled ? 'Disable authentication method' : 'Enable authentication method'}>
              <Switch
                checked={isEnabled}
                onChange={(e) => {
                  e.stopPropagation();
                  handleToggleMethod(accessPoint);
                }}
                disabled={isUpdating}
                size="medium"
                sx={{
                  '& .MuiSwitch-switchBase': {
                    '&.Mui-checked': {
                      color: theme.palette.common.white,
                      '& + .MuiSwitch-track': {
                        backgroundColor: theme.palette.primary.main,
                        opacity: 1,
                      },
                    },
                  },
                  '& .MuiSwitch-track': {
                    backgroundColor: theme.palette.grey[300],
                    opacity: 1,
                  },
                  '& .MuiSwitch-thumb': {
                    backgroundColor: theme.palette.common.white,
                    boxShadow: theme.shadows[1],
                  },
                }}
              />
            </Tooltip>
          </Box>
        )}

        <Stack spacing={2}>
          {/* Header */}
          <Box>
            <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 0.5 }}>
              <Typography variant="h6" fontWeight={600}>
                {authInfo.name}
              </Typography>
              {accessPoint.recommended && (
                <Chip
                  label="Recommended"
                  size="small"
                  sx={{
                    backgroundColor: (theme) => alpha(theme.palette.success.main, 0.1),
                    color: 'success.main',
                    fontWeight: 600,
                    fontSize: '0.75rem',
                    height: 22,
                  }}
                />
              )}
              {accessPoint.isBeta && (
                <Chip
                  label="Beta"
                  size="small"
                  sx={{
                    backgroundColor: (theme) => alpha(theme.palette.warning.main, 0.1),
                    color: 'warning.main',
                    fontWeight: 600,
                    fontSize: '0.75rem',
                    height: 22,
                  }}
                />
              )}
            </Stack>
            <Typography variant="body2" color="text.secondary">
              {authInfo.description}
            </Typography>
          </Box>

          {/* Action Button */}
          <Box>
            {isDisabled ? (
              <Typography
                variant="body2"
                sx={{
                  color: theme.palette.text.disabled,
                  fontWeight: 500,
                }}
              >
                Disabled
              </Typography>
            ) : isConfigured ? (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: theme.palette.success.main,
                    fontWeight: 500,
                  }}
                >
                  Configured
                </Typography>
                {accessPoint.type !== AccessPointConfigType.APIKeyFlow && (
                  <Button
                    size="small"
                    variant="text"
                    endIcon={<ChevronRight size={16} />}
                    sx={{
                      textTransform: 'none',
                      color: theme.palette.primary.main,
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleConfigure(accessPoint.id);
                    }}
                  >
                    Edit
                  </Button>
                )}
              </Box>
            ) : needsConfig ? (
              <Button
                variant="outlined"
                size="small"
                endIcon={<ChevronRight size={16} />}
                sx={{
                  textTransform: 'none',
                  borderColor: theme.palette.warning.main,
                  color: theme.palette.warning.main,
                  '&:hover': {
                    borderColor: theme.palette.warning.dark,
                    backgroundColor: alpha(theme.palette.warning.main, 0.04),
                  },
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleConfigure(accessPoint.id);
                }}
              >
                <AlertCircle size={16} style={{ marginRight: 8 }} />
                Configure to enable for end users
              </Button>
            ) : (
              <Button
                variant="contained"
                size="small"
                endIcon={<ChevronRight size={16} />}
                sx={{
                  textTransform: 'none',
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleConfigure(accessPoint.id);
                }}
              >
                Configure
              </Button>
            )}
          </Box>
        </Stack>
      </Card>
    );
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      disableEscapeKeyDown={false}
      ModalProps={{
        onBackdropClick: (event) => {
          event.stopPropagation();
          // Prevent closing on backdrop click
        },
      }}
      sx={{
        '& .MuiDrawer-paper': {
          width: 600,
          maxWidth: '90vw',
        },
      }}
    >
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box
          sx={{
            p: 3,
            borderBottom: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Box>
            {/* First row: Back button (if needed), Logo, Title, Close button */}
            <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 1 }}>
              <Stack direction="row" alignItems="center" spacing={1.5}>
                {isConfiguring && (
                  <IconButton 
                    onClick={handleBackToList}
                    size="small"
                    sx={{ 
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.08),
                      }
                    }}
                  >
                    <ArrowLeft size={20} />
                  </IconButton>
                )}
                <Box
                  component="img"
                  src={connector.icon}
                  alt={connector.name}
                  sx={{
                    width: 32,
                    height: 32,
                    objectFit: 'contain',
                  }}
                />
                <Typography variant="h5" fontWeight={600}>
                  {isConfiguring && selectedAccessPoint 
                    ? `Configure ${selectedAccessPoint.label}`
                    : `${connector.name} Configuration`
                  }
                </Typography>
              </Stack>
              <IconButton onClick={onClose} size="small">
                <X size={20} />
              </IconButton>
            </Stack>
            
            {/* Second row: Description */}
            <Typography 
              variant="body2" 
              color="text.secondary"
              sx={{ 
                ml: isConfiguring ? 6.5 : 5.5, // Align with title, accounting for back button
              }}
            >
              {isConfiguring && selectedAccessPoint
                ? selectedAccessPoint.description || `Using ${selectedAccessPoint.label} credentials ensures your logo appears in the pop-up during user authentication, providing a branded experience.`
                : `Manage authentication methods and data models for your ${connector.name} integration`
              }
            </Typography>
          </Box>
        </Box>

        {/* Content */}
        <Box sx={{ flex: 1, overflow: 'auto' }}>
          {isConfiguring && selectedAccessPoint ? (
            // Configuration Form View
            <form onSubmit={handleSubmit(onSubmitConfiguration)}>
              <Box sx={{ p: 3 }}>
                {/* Error Summary */}
                {Object.keys(errors).length > 0 && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    <Typography variant="body2" fontWeight={600} sx={{ mb: 1 }}>
                      Please correct the following errors:
                    </Typography>
                    <ul style={{ margin: 0, paddingLeft: '20px' }}>
                      {Object.entries(errors).map(([segmentKey, segmentErrors]: [string, any]) => (
                        Object.entries(segmentErrors).map(([fieldKey, error]: [string, any]) => {
                          const field = segments
                            .flatMap((s: any) => s.fieldTypeConfigs || [])
                            .find((f: any) => f.property.endsWith(fieldKey));
                          return (
                            <li key={`${segmentKey}.${fieldKey}`}>
                              <Typography variant="caption">
                                {field?.label || fieldKey}: {error?.message}
                              </Typography>
                            </li>
                          );
                        })
                      ))}
                    </ul>
                  </Alert>
                )}

                {/* Configuration Info */}
                {configContainer?.assistanceInfo?.processSteps?.[0] && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" fontWeight={600} sx={{ mb: 1 }}>
                      {configContainer.assistanceInfo.processSteps[0].title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {configContainer.assistanceInfo.processSteps[0].description}
                    </Typography>
                    {configContainer?.links?.[0] && (
                      <Link
                        href={configContainer.links[0].href}
                        target="_blank"
                        sx={{
                          mt: 1,
                          display: 'inline-block',
                          color: theme.palette.primary.main,
                          textDecoration: 'none',
                          fontSize: '0.875rem',
                          '&:hover': {
                            textDecoration: 'underline',
                          },
                        }}
                      >
                        {configContainer.links[0].name}
                      </Link>
                    )}
                  </Box>
                )}

                {/* Form Fields */}
                <Stack spacing={3}>
                  {segments.map((segment: any) => (
                    <Box key={segment.key}>
                      {segment.label && (
                        <Typography
                          variant="subtitle1"
                          fontWeight={600}
                          sx={{ mb: 2 }}
                        >
                          {segment.label}
                        </Typography>
                      )}
                      <Stack spacing={3}>
                        {segment.fieldTypeConfigs?.map((field: any) => {
                          const propertyName = field.property.split('/').pop() || field.property;
                          const fieldError = errors[segment.key]?.[propertyName];
                          
                          // Skip button type fields and file fields for now
                          if (field.type === 'BUTTON' || field.type === 'FILE') {
                            return null;
                          }
                          
                          return (
                            <Box key={field.property}>
                              <Typography
                                variant="body2"
                                fontWeight={600}
                                sx={{ mb: 0.5 }}
                              >
                                {field.label}
                                {field.required && (
                                  <Typography
                                    component="span"
                                    sx={{
                                      color: theme.palette.error.main,
                                      ml: 0.5,
                                    }}
                                  >
                                    *
                                  </Typography>
                                )}
                              </Typography>
                              {field.description && (
                                <Typography
                                  variant="caption"
                                  color="text.secondary"
                                  sx={{ display: 'block', mb: 1 }}
                                >
                                  {field.description}
                                </Typography>
                              )}
                              <Controller
                                name={`${segment.key}.${propertyName}`}
                                control={control}
                                render={({ field: { onChange, value, onBlur }, fieldState: { isTouched } }) => {
                                  // Show validation hints for fields with patterns
                                  const getHelperText = () => {
                                    if (fieldError?.message) {
                                      return fieldError.message;
                                    }
                                    if (field.required && !value && isSubmitted) {
                                      return 'This field is required';
                                    }
                                    // Show format hints for specific fields when focused
                                    if (isTouched && !fieldError && field.placeholder) {
                                      return `Format: ${field.placeholder}`;
                                    }
                                    return '';
                                  };
                                  
                                  const isPasswordField = field.property.includes('Secret') || field.property.includes('password');
                                  const fieldId = `${segment.key}-${propertyName}`;
                                  const shouldShowPassword = showPassword[fieldId] || false;
                                  
                                  return (
                                    <TextField
                                      fullWidth
                                      value={value || ''}
                                      onChange={(e) => {
                                        onChange(e);
                                        // Trigger validation on change for immediate feedback
                                        if (field.validations && field.validations.length > 0) {
                                          trigger(`${segment.key}.${propertyName}`);
                                        }
                                      }}
                                      onBlur={onBlur}
                                      placeholder={field.placeholder || `Enter ${field.label}`}
                                      error={!!fieldError || (field.required && !value && isSubmitted)}
                                      helperText={getHelperText()}
                                      size="medium"
                                      required={field.required}
                                      type={isPasswordField ? (shouldShowPassword ? 'text' : 'password') : 'text'}
                                      InputProps={{
                                        endAdornment: isPasswordField && value ? (
                                          <IconButton
                                            size="small"
                                            onClick={() => {
                                              setShowPassword(prev => ({
                                                ...prev,
                                                [fieldId]: !prev[fieldId]
                                              }));
                                            }}
                                            edge="end"
                                            sx={{
                                              mr: -0.5,
                                              color: theme.palette.text.secondary,
                                              '&:hover': {
                                                color: theme.palette.text.primary,
                                              }
                                            }}
                                          >
                                            {shouldShowPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                                          </IconButton>
                                        ) : null,
                                      }}
                                      inputProps={{
                                        'aria-label': field.label,
                                        'aria-required': field.required,
                                        'aria-invalid': !!fieldError,
                                        'aria-describedby': fieldError ? `${field.property}-error` : undefined,
                                      }}
                                      sx={{
                                        '& .MuiOutlinedInput-root': {
                                          backgroundColor: theme.palette.background.paper,
                                          '&.Mui-error': {
                                            '& .MuiOutlinedInput-notchedOutline': {
                                              borderColor: theme.palette.error.main,
                                            },
                                          },
                                          '&:hover': {
                                            '& .MuiOutlinedInput-notchedOutline': {
                                              borderColor: theme.palette.primary.main,
                                            },
                                          },
                                          '&.Mui-focused': {
                                            '& .MuiOutlinedInput-notchedOutline': {
                                              borderColor: fieldError 
                                                ? theme.palette.error.main 
                                                : theme.palette.primary.main,
                                            },
                                          },
                                        },
                                        '& .MuiFormHelperText-root': {
                                          marginTop: 0.5,
                                          fontSize: '0.75rem',
                                          transition: 'color 0.2s',
                                        },
                                        '& .MuiFormHelperText-root.Mui-error': {
                                          color: `${theme.palette.error.main} !important`,
                                        },
                                      }}
                                    />
                                  );
                                }}
                              />
                            </Box>
                          );
                        })}
                      </Stack>
                      
                      {/* Segment Links */}
                      {segment.links && segment.links.length > 0 && (
                        <Box sx={{ mt: 2 }}>
                          {segment.links.map((link: any, idx: number) => (
                            <Link
                              key={idx}
                              href={link.href}
                              target="_blank"
                              sx={{
                                color: theme.palette.primary.main,
                                textDecoration: 'none',
                                fontSize: '0.875rem',
                                '&:hover': {
                                  textDecoration: 'underline',
                                },
                              }}
                            >
                              {link.name}
                            </Link>
                          ))}
                        </Box>
                      )}
                    </Box>
                  ))}
                </Stack>
              </Box>
            </form>
          ) : (
            // List View
            <>
              {/* Info Banner */}
              <Box sx={{ px: 3, pt: 2 }}>
                <Alert
                  severity="info"
                  icon={<Sparkles size={20} />}
                  sx={{
                    backgroundColor: alpha(theme.palette.info.main, 0.08),
                    border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
                    '& .MuiAlert-icon': {
                      color: theme.palette.info.main,
                    },
                  }}
                >
                  <Typography variant="body2">
                    Unizo supports {serviceProfileAccessPoints.length || 3} authentication methods for {connector.name}.
                    Configure whichever you want, they'll be available in both API and Connect UI,
                    allowing customers to connect integrations with ease.
                  </Typography>
                </Alert>
              </Box>

              <Box sx={{ p: 3 }}>
                {isLoading ? (
                  <Stack spacing={2}>
                    {[1, 2, 3].map((i) => (
                      <Skeleton key={i} variant="rectangular" height={140} sx={{ borderRadius: 2 }} />
                    ))}
                  </Stack>
                ) : (
                  <Stack spacing={2}>
                    {sortedAccessPoints.map((ap) => (
                      <AuthMethodCard key={ap.id} accessPoint={ap} />
                    ))}
                  </Stack>
                )}
              </Box>
            </>
          )}
        </Box>

        {/* Footer */}
        <Box
          sx={{
            p: 3,
            borderTop: `1px solid ${theme.palette.divider}`,
            backgroundColor: theme.palette.background.default,
          }}
        >
          {isConfiguring ? (
            // Configuration Form Footer
            <Stack direction="row" spacing={2} justifyContent="flex-end">
              <Button 
                variant="text" 
                onClick={handleBackToList}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                type="submit"
                onClick={handleSubmit(onSubmitConfiguration)}
                disabled={isSubmitting}
                sx={{
                  backgroundColor: theme.palette.grey[900],
                  color: theme.palette.common.white,
                  minWidth: 120,
                  '&:hover': {
                    backgroundColor: theme.palette.grey[800],
                  },
                  '&:disabled': {
                    backgroundColor: theme.palette.action.disabledBackground,
                  },
                }}
              >
                {isSubmitting ? (
                  <>
                    <CircularProgress size={20} sx={{ mr: 1, color: 'inherit' }} />
                    Saving...
                  </>
                ) : (
                  'Submit'
                )}
              </Button>
            </Stack>
          ) : (
            // List View Footer
            <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
              <Typography variant="body2" color="text.secondary">
                Need help? Check out the{' '}
                <Link
                  href="https://docs.unizo.io/guides/"
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{
                    color: theme.palette.primary.main,
                    textDecoration: 'none',
                    fontWeight: 500,
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  }}
                >
                  Authentication Guides
                </Link>{' '}
                for integration steps.
              </Typography>
              <Button variant="text" onClick={onClose}>
                Close
              </Button>
            </Stack>
          )}
        </Box>
      </Box>
    </Drawer>
  );
}