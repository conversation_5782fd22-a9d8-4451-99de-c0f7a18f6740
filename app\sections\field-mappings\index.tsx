import { Grid, Typography, Button } from '@mui/material';
import { Plus } from 'lucide-react';
import PageCard from 'components/cards/PageCard';

export default function FieldMappingsPage() {
  return (
    <Grid container spacing={3}>
      {/* Header Section */}
      <Grid item xs={12}>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item>
            <Typography variant="h3" fontWeight={700} gutterBottom>
              Field Mappings
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Define how data fields map between your system and external integrations.
            </Typography>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              startIcon={<Plus />}
            >
              Create Mapping
            </Button>
          </Grid>
        </Grid>
      </Grid>

      {/* Main Content */}
      <Grid item xs={12}>
        <PageCard>
          <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 8 }}>
            No field mappings configured yet. Create your first mapping to get started.
          </Typography>
        </PageCard>
      </Grid>
    </Grid>
  );
}