import { useMemo, useState } from "react";
import { List, Skeleton, Stack } from "@mui/material"

import MenuItem from "./item";

type MenuItemTypes = {
   id: string,
   title: string,
   type?: 'item' | 'collapse' | 'dropdown',
   children?: MenuItemTypes[],
   level?: number,
   onClick?: () => void,
   disabled?: boolean,
}

type MenuProps = {
   items?: MenuItemTypes[]
   onValueChange?: (value: MenuItemTypes) => void
   selected?: any
}

export const Menu = ({
   selected: selectedProp,
   onValueChange: onChangeProp,
   ...props
}: MenuProps) => {
   const [selectedLocal, setSelectedLocal] = useState(() => {
      return props?.items?.length ? props?.items?.[0]?.id : null
   })

   const selected = selectedProp ?? selectedLocal

   const onChange = (newVal: MenuItemTypes) => {
      setSelectedLocal(newVal.id)
      onChangeProp && onChangeProp(newVal)
   }
   
   const navCollapse = useMemo(() => {
      return props?.items?.map(({ id, title, type, disabled = false, released ,isNew}: any) => {
        
         return (
            <MenuItem
               key={id}
               item={{
                  id,
                  title,
                  type: type ?? 'item',
                  onClick: onChange,
                  isSelected: selected === id,
                  disabled,
                  released,
                  isNew
               }}
               level={1}
            /> satisfies any
         )
      })
   }, [props?.items, selected])

   return (
      <List
         sx={{ mb: 1.5, py: 0, zIndex: 0 }}
      >
         {navCollapse}
      </List>
   )
}

export const MenuSkeleton = () => {
   return (
     <Stack sx={{ px: 2 }} gap={3}>
       <Skeleton variant="rounded" height={30} />
       <Skeleton variant="rounded" height={30} />
     </Stack>
   )
 }