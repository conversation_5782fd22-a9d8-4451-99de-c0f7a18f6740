import PropTypes from 'prop-types';
import { forwardRef, useEffect } from 'react';
import { Link, useLocation, matchPath } from '@remix-run/react';

// material-ui
import { Box, Stack, Tooltip, useTheme, alpha } from '@mui/material';
import Chip from '@mui/material/Chip';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';

// project import

export default function MenuItem(props: any) {
   const { item, level } = props;
   const theme: any = useTheme();

   const isSelected = item?.isSelected ?? false
   const drawerOpen = true;
   let itemTarget = '_self';
   let listItemProps: any = { component: forwardRef((props, ref: any) => <Link ref={ref} {...props} to={item.url} target={itemTarget} />) };
   if (item?.external) {
      listItemProps = { component: 'a', href: item.url, target: itemTarget };
   }

   const Icon = item?.icon;
   const itemIcon = item?.icon ? <Icon style={{ fontSize: '1rem' }} /> : false;

   const textColor = 'text.primary';
   const iconSelectedColor = 'primary.main';

   const newColorCheck = theme?.palette?.grey[0]

   // console.log(theme.palette,"theme.palette.primary.maintheme.palette.primary.main")
   return (
      <Stack
         direction={'row'}
         sx={{
            position: 'relative'
         }}
      >
         <ListItemButton
            // {...listItemProps}
            disabled={!item?.released}
            onClick={() => item?.onClick && item?.onClick(item)}
            selected={isSelected}
            sx={{
               zIndex: 1201,
               pl: drawerOpen ? 2.5 : 1.5,
               py: 1.25,
               borderRadius: 0,
               mx: 1,
               my: 0.5,
               transition: 'all 0.2s ease',
               ...(drawerOpen && {
                  '&:hover': {
                     bgcolor: theme.palette.mode === 'dark'
                        ? alpha(theme.palette.primary.main, 0.08)
                        : alpha(theme.palette.primary.main, 0.04),
                     transform: 'translateX(2px)'
                  },
                  '&.Mui-selected': {
                     bgcolor: theme.palette.mode === 'dark'
                        ? alpha(theme.palette.primary.main, 0.16)
                        : alpha(theme.palette.primary.main, 0.08),
                     borderLeft: `2px solid ${theme.palette.primary.main}`,
                     color: iconSelectedColor,
                     fontWeight: 600,
                     '&:hover': {
                        color: iconSelectedColor,
                        bgcolor: theme.palette.mode === 'dark'
                           ? alpha(theme.palette.primary.main, 0.2)
                           : alpha(theme.palette.primary.main, 0.12),
                        transform: 'translateX(2px)'
                     },
                     '& .MuiTypography-root': {
                        fontWeight: 600,
                        lineHeight: 1.2
                     }
                  }
               }),
               ...(!item?.released && {
                  opacity: 0.6,
                  cursor: 'not-allowed',
                  '&:hover': {
                     bgcolor: 'transparent'
                  }
               }),
               position: 'relative',
            }}
         >
            {itemIcon && (
               <ListItemIcon
                  sx={{
                     minWidth: 28,
                     color: isSelected ? iconSelectedColor : textColor,
                  }}
               >
                  {itemIcon}
               </ListItemIcon>
            )}
            {item?.title && (
               <ListItemText
                  sx={{
                     margin: 0,
                     '& .MuiListItemText-primary': {
                        margin: 0
                     }
                  }}
                  primary={
                     <Typography className='truncate' variant="h6" sx={{
                        color: isSelected ? iconSelectedColor : textColor,
                        lineHeight: 1.2,
                        margin: 0
                     }}>
                        {item?.title}
                     </Typography>
                  }
               />
            )}
            {item?.isNew && (
               <Stack marginRight='5px'>
                  <Chip variant={('light') as any}
                     size="small"
                     label='New'
                     color={"info"}
                     sx={{
                        height: '18px',
                        '& .MuiChip-label': {
                           fontSize: '12px',
                           padding: '0 6px',
                           lineHeight: 1.2
                        }
                     }}
                  />
               </Stack>

            )}
            {item?.disabled && (item?.released) && (
               <Tooltip
                  title='Your plan does not support this category'
                  placement='top'
               >

                  <ListItemIcon
                     sx={{
                        color: isSelected ? iconSelectedColor : 'secondary.main',
                        mr: isSelected ? -.3 : '',
                     }}
                  >
                     <LockOutlinedIcon sx={{ fontSize: '1.1rem' }} />
                  </ListItemIcon>
               </Tooltip>
            )}
            {!item?.released && (
               <Chip label='Coming soon' size='small' color='success' className='ml-2 scale-[.8]'

               />
            )}

         </ListItemButton>
      </Stack>
   );
}

MenuItem.propTypes = { item: PropTypes.object, level: PropTypes.number };
