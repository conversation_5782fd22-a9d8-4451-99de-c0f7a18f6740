import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>ack,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Avatar,
  Divider,
  useTheme,
  alpha,
  CircularProgress,
} from '@mui/material';
import { CheckCircle, Rocket, ArrowRight } from 'lucide-react';
import { useGettingStarted } from '../context/GettingStartedContext';
import { toast } from 'sonner';
import confetti from 'canvas-confetti';
import { useNavigate } from '@remix-run/react';
import { getDomainByValue } from 'data/domains';

interface GetStartedFinalProps {
  onNext: () => void;
  onBack: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

export default function GetStartedFinal({ onNext, onBack, isLastStep }: GetStartedFinalProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const { selectedCategories, selectedServices, setIsLoading, isLoading } = useGettingStarted();
  const [isCreating, setIsCreating] = useState(false);

  const handleGetStarted = async () => {
    setIsCreating(true);
    
    try {
      // Simulate API call to create tenant and services
      // In real implementation, you would call the actual APIs here
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Fire confetti
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 },
        colors: [theme.palette.primary.main, theme.palette.secondary.main],
      });
      
      toast.success('Your Unizo workspace is ready!');
      
      // Navigate to dashboard
      setTimeout(() => {
        navigate('/console/dashboard');
      }, 1000);
    } catch (error) {
      console.error('Failed to create workspace:', error);
      toast.error('Failed to create workspace. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  const renderCategorySummary = () => {
    return selectedCategories.map(categoryCode => {
      const categoryServices = selectedServices.filter(s => s.category === categoryCode);
      return (
        <Box key={categoryCode} sx={{ mb: 3 }}>
          <Typography variant="subtitle1" fontWeight={600} gutterBottom>
            {getDomainByValue(categoryCode)?.label || categoryCode}
          </Typography>
          <Grid container spacing={1}>
            {categoryServices.map(service => (
              <Grid item key={service.serviceProfileId}>
                <Chip
                  avatar={
                    <Avatar
                      src={service.logo || `https://logo.clearbit.com/${service.name.toLowerCase().replace(/\s+/g, '')}.com`}
                      sx={{ width: 20, height: 20 }}
                    >
                      {service.name.charAt(0)}
                    </Avatar>
                  }
                  label={service.name}
                  size="small"
                  sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.08) }}
                />
              </Grid>
            ))}
          </Grid>
        </Box>
      );
    });
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', position: 'relative' }}>
      {/* Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Box
          sx={{
            width: 80,
            height: 80,
            borderRadius: '50%',
            backgroundColor: alpha(theme.palette.primary.main, 0.1),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto',
            mb: 3,
          }}
        >
          <Rocket size={40} color={theme.palette.primary.main} />
        </Box>
        <Typography variant="h1" fontWeight={700} gutterBottom sx={{ fontSize: '2.5rem' }}>
          Ready to Launch!
        </Typography>
        <Typography variant="h4" color="text.secondary" sx={{ fontSize: '1.25rem', fontWeight: 400 }}>
          Review your configuration and get started with Unizo
        </Typography>
      </Box>

      {/* Summary Card */}
      <Card
        sx={{
          flex: 1,
          mb: '80px',
          boxShadow: theme.shadows[2],
          border: `1px solid ${theme.palette.divider}`,
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Stack spacing={3}>
            {/* Stats */}
            <Grid container spacing={3}>
              <Grid item xs={6}>
                <Box
                  sx={{
                    p: 2,
                    backgroundColor: alpha(theme.palette.primary.main, 0.08),
                    borderRadius: 1,
                    textAlign: 'center',
                  }}
                >
                  <Typography variant="h3" color="primary" fontWeight={700}>
                    {selectedCategories.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    API Categories
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box
                  sx={{
                    p: 2,
                    backgroundColor: alpha(theme.palette.secondary.main, 0.08),
                    borderRadius: 1,
                    textAlign: 'center',
                  }}
                >
                  <Typography variant="h3" color="secondary" fontWeight={700}>
                    {selectedServices.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Connectors
                  </Typography>
                </Box>
              </Grid>
            </Grid>

            <Divider />

            {/* Selected Items */}
            <Box>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Your Configuration
              </Typography>
              {renderCategorySummary()}
            </Box>

            {/* What's Next */}
            <Box
              sx={{
                p: 2,
                backgroundColor: alpha(theme.palette.info.main, 0.08),
                borderRadius: 1,
                border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
              }}
            >
              <Stack direction="row" spacing={2} alignItems="flex-start">
                <CheckCircle size={20} color={theme.palette.info.main} />
                <Box>
                  <Typography variant="subtitle2" fontWeight={600} color="info.main" gutterBottom>
                    What happens next?
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    We'll create your Unizo workspace with the selected integrations. 
                    You'll be redirected to your dashboard where you can configure each 
                    connector, set up webhooks, and start building integrations.
                  </Typography>
                </Box>
              </Stack>
            </Box>
          </Stack>
        </CardContent>
      </Card>

      {/* Floating Bottom Bar */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 0,
          left: '280px', // Account for sidebar width
          right: 0,
          backgroundColor: theme.palette.background.paper,
          borderTop: `1px solid ${theme.palette.divider}`,
          boxShadow: theme.shadows[4],
          zIndex: 100,
        }}
      >
        <Box
          sx={{
            px: 4,
            py: 2,
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <Button
            variant="outlined"
            onClick={onBack}
            disabled={isCreating}
            sx={{ minWidth: 120 }}
          >
            Back
          </Button>
          <Button
            variant="contained"
            onClick={handleGetStarted}
            disabled={isCreating}
            endIcon={isCreating ? <CircularProgress size={20} /> : <ArrowRight size={20} />}
            sx={{
              minWidth: 180,
              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
              '&:hover': {
                background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
              },
            }}
          >
            {isCreating ? 'Creating Workspace...' : 'Get Started'}
          </Button>
        </Box>
      </Box>
    </Box>
  );
}

// Fix the import issue by adding a simple Chip component
const Chip = ({ avatar, label, size, sx }: any) => (
  <Box
    sx={{
      display: 'inline-flex',
      alignItems: 'center',
      gap: 0.5,
      px: 1.5,
      py: 0.5,
      borderRadius: 2,
      fontSize: size === 'small' ? '0.75rem' : '0.875rem',
      ...sx,
    }}
  >
    {avatar}
    <span>{label}</span>
  </Box>
);