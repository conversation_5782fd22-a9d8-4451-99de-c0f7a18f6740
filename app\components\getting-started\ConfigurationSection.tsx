import React from 'react';
import {
  Box,
  Typography,
  Button,
  Stack,
  useTheme,
  Grid,
  alpha,
} from '@mui/material';

interface ConfigurationSectionProps {
  stepNumber?: string;
  title: string;
  description: string;
  isConfigured: boolean;
  onGetStarted: () => void;
  configuredContent?: React.ReactNode;
  secondaryOption?: {
    title: string;
    content: React.ReactNode;
  };
  isaddView?: boolean
}

export default function ConfigurationSection({
  stepNumber,
  title,
  description,
  isConfigured,
  onGetStarted,
  isaddView = false,
  configuredContent,
  secondaryOption,
}: ConfigurationSectionProps) {
  const theme = useTheme();

  if (!isConfigured) {
    // Getting Started State - Single column layout
    return (
      <Box sx={{ maxWidth: 800 }}>
        <Stack spacing={3}>
          <Box>
            <Typography variant="h5" fontWeight={600} gutterBottom>
              {title}
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: theme.palette.mode === 'light'
                  ? theme.palette.grey[600]
                  : theme.palette.text.secondary,
                mb: 3,
              }}
            >
              {description}
            </Typography>
            <Button
              variant="contained"
              size="medium"
              onClick={() => onGetStarted()}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                px: 4,
                py: 1.5,
              }}
            >
              Click here to get started
            </Button>
          </Box>
        </Stack>
      </Box>
    );
  }

  // Configured State - Show the configured content with max width constraint
  return (
    <Box>
      {configuredContent}
      {secondaryOption && (
        <Box sx={{ mt: 3 }}>
          {/* OR Badge */}
          <Box 
            sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              mb: 3,
            }}
          >
            <Box
              sx={{
                px: 2,
                py: 0.5,
                backgroundColor: theme.palette.background.default,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 10,
                boxShadow: `0 0 0 4px ${theme.palette.background.paper}`,
              }}
            >
              <Typography 
                variant="caption" 
                sx={{ 
                  color: theme.palette.text.secondary,
                  fontWeight: 600,
                  letterSpacing: 0.5,
                  textTransform: 'uppercase',
                }}
              >
                OR
              </Typography>
            </Box>
          </Box>

          {/* Secondary Option Content */}
          <Box 
            sx={{ 
              maxWidth: 800,
              backgroundColor: theme.palette.background.default,
              borderRadius: 2,
              p: 3,
              border: `1px solid ${theme.palette.divider}`,
            }}
          >
            <Typography variant="h5" fontWeight={600} gutterBottom>
              {secondaryOption.title}
            </Typography>
            {secondaryOption.content}
          </Box>
        </Box>
      )}
    </Box>
  );
}