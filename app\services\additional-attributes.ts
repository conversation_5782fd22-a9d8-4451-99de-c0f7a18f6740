import { AdditionalFields } from "types/additional-attributes";
import platformfetchInstance from "utils/api/fetchinstance/platform-fetch-Instance";


export const additionalAttributesClient = {
  getAll: () => {
    return platformfetchInstance.get(`/additionalFields`, {
      params: { limit: 100 }
    })
  },
  create: (payload: AdditionalFields.CreatePayload) => {
    return platformfetchInstance.post(`/additionalFields`, payload)
  },
  delete: (id: AdditionalFields.Root['id']) => {
    return platformfetchInstance.delete(`/additionalFields/${id}`)
  }
};