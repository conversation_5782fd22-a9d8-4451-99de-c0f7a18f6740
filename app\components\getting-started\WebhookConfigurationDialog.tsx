import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  TextField,
  Button,
  Stack,
  IconButton,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  useTheme,
  alpha,
} from '@mui/material';
import { X } from 'lucide-react';

interface WebhookConfigurationDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

const categories = [
  { value: 'PLATFORM', label: 'Platform' },
  { value: 'SCM', label: 'Source Control' },
  { value: 'TICKETING', label: 'Ticketing' },
  { value: 'INCIDENT', label: 'Incident Management' },
];

export default function WebhookConfigurationDialog({
  open,
  onClose,
  onSubmit,
}: WebhookConfigurationDialogProps) {
  const theme = useTheme();
  const [formData, setFormData] = useState({
    type: '',
    url: '',
    secret: '',
    apiKey: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    // Reset form
    setFormData({
      type: '',
      url: '',
      secret: '',
      apiKey: '',
    });
  };

  const handleChange = (field: string) => (event: any) => {
    setFormData({
      ...formData,
      [field]: event.target.value,
    });
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
        },
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          pb: 1,
        }}
      >
        <Typography variant="h5" fontWeight={600}>
          Create Webhook
        </Typography>
        <IconButton
          onClick={onClose}
          size="small"
          sx={{
            color: theme.palette.text.secondary,
            '&:hover': {
              backgroundColor: alpha(theme.palette.action.active, 0.04),
            },
          }}
        >
          <X size={20} />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        <Box sx={{ py: 2 }}>
          <Alert severity="info" sx={{ mb: 3 }}>
            We'll send a POST request to the URL below with details of any subscribed webhooks. 
            You can also specify which data format you'd like to receive (application/json, etc). 
            More information can be found in our developer documentation.
          </Alert>

          <form onSubmit={handleSubmit}>
            <Stack spacing={3}>
              {/* Category Select */}
              <Box>
                <Typography variant="h6" fontSize="0.875rem" fontWeight={600} gutterBottom>
                  Select Category
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                  Select the Category that best suits your needs.
                </Typography>
                <FormControl fullWidth>
                  <InputLabel id="category-label">Category</InputLabel>
                  <Select
                    labelId="category-label"
                    value={formData.type}
                    onChange={handleChange('type')}
                    label="Category"
                  >
                    {categories.map((category) => (
                      <MenuItem key={category.value} value={category.value}>
                        {category.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              {/* Callback URL */}
              <Box>
                <Typography variant="h6" fontSize="0.875rem" fontWeight={600} gutterBottom>
                  Callback Url
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                  Enter the Callback Url.
                </Typography>
                <TextField
                  fullWidth
                  placeholder="https://example.com"
                  value={formData.url}
                  onChange={handleChange('url')}
                  name="url"
                />
              </Box>

              {/* Secret */}
              <Box>
                <Typography variant="h6" fontSize="0.875rem" fontWeight={600} gutterBottom>
                  Secret
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                  Encrypt your data securely using a secret key.
                </Typography>
                <TextField
                  fullWidth
                  placeholder="********"
                  value={formData.secret}
                  onChange={handleChange('secret')}
                  name="secret"
                  type="password"
                />
              </Box>

              {/* API Key */}
              <Box>
                <Typography variant="h6" fontSize="0.875rem" fontWeight={600} gutterBottom>
                  API Key
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                  Enter the API key for the callback Url.
                </Typography>
                <TextField
                  fullWidth
                  placeholder="********"
                  value={formData.apiKey}
                  onChange={handleChange('apiKey')}
                  name="apiKey"
                  type="password"
                />
              </Box>
            </Stack>
          </form>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Stack direction="row" spacing={2}>
          <Button
            variant="text"
            onClick={onClose}
            sx={{
              textTransform: 'none',
              color: theme.palette.text.primary,
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit}
            sx={{
              textTransform: 'none',
              fontWeight: 500,
            }}
          >
            Submit
          </Button>
        </Stack>
      </DialogActions>
    </Dialog>
  );
}