import React, { useState } from 'react';
import {
  Box,
  Stack,
  Typography,
  Chip,
  useTheme,
  alpha,
  Button,
  Divider,
  Paper,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  Tooltip,
} from '@mui/material';
import {
  <PERSON>,
  Settings,
  ChevronRight,
  Check,
  Key,
  UserCheck,
  Link2,
  Database,
  Code2,
  Circle,
  CheckCircle,
  Info,
} from 'lucide-react';
import { AccessPointConfigType } from 'hooks/api/use-accesspoint-config/useGetAccessPoint';
import useServiceConfig from 'store/setup-service';
import { State } from 'hooks/useStatus';
import DataModelsConfiguration from '../data-models';

interface AuthMethod {
  id: string;
  type: AccessPointConfigType;
  label: string;
  description?: string;
  icon: React.ReactNode;
  configured: boolean;
  features?: {
    version?: string;
    sandboxSupported?: boolean;
    productionSupported?: boolean;
    fieldMappings?: number;
    customFields?: number;
    lastUpdated?: string;
  };
}

interface ConnectorConfigurationSummaryProps {
  serviceProfile: any;
  accessPoints: any[];
  onEdit?: () => void;
  onConfigureAuth?: (authMethod: AuthMethod) => void;
  hasVersionSupport?: boolean;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`connector-tabpanel-${index}`}
      aria-labelledby={`connector-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

export default function ConnectorConfigurationSummary({
  serviceProfile,
  accessPoints,
  onEdit,
  onConfigureAuth,
  hasVersionSupport = false,
}: ConnectorConfigurationSummaryProps) {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const { setSelectedTypeConfig } = useServiceConfig();
const showDataModelsTab = ["SCM", "TICKETING"].includes(serviceProfile?.type); // or your own logic
  const isServiceActive = serviceProfile?.service?.state === State.ACTIVE;

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Transform access points to auth methods
  const authMethods: AuthMethod[] = React.useMemo(() => {
    const authTypeIcons = {
      [AccessPointConfigType.OAuthFlow]: <Shield size={16} />,
      [AccessPointConfigType.OAuthPasswordFlow]: <UserCheck size={16} />,
      [AccessPointConfigType.APIKeyFlow]: <Key size={16} />,
      [AccessPointConfigType.AppFlow]: <Link2 size={16} />,
    };

    return accessPoints.map((ap) => ({
      id: ap.id,
      type: ap.type,
      label: ap.label,
      description: ap.description,
      icon: authTypeIcons[(ap.type as AccessPointConfigType)] || <Shield size={16} />,
      configured: !!ap.accessPoint?.id,
      features: {
        version: ap.accessPoint?.version || ap.version || 'v2.0',
        sandboxSupported: ap.sandboxSupported !== false,
        productionSupported: ap.productionSupported !== false,
        fieldMappings: ap.fieldMappings?.length || 0,
        customFields: ap.customFields?.length || 0,
        lastUpdated: ap.accessPoint?.updatedAt || ap.updatedAt,
      },
    }));
  }, [accessPoints]);

  const showApiVersionsTab = hasVersionSupport && authMethods.some(m => m.configured && m.features?.version);

  return (
    <Box>
      {/* Header Section */}
      <Stack mb={3}>
        <Typography variant="h6" fontWeight={600}>
          {serviceProfile?.name || 'Service'} Configuration
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
          Manage authentication methods{showApiVersionsTab ? ', API versions,' : ''} and data models for your {serviceProfile?.name || 'service'} integration
        </Typography>
      </Stack>

      {/* Tabs Container */}
      <Box>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            minHeight: 42,
            '& .MuiTab-root': {
              textTransform: 'none',
              minHeight: 42,
              fontWeight: 500,
              fontSize: '0.875rem',
              '&.Mui-selected': {
                color: theme.palette.text.primary,
              },
            },
            '& .MuiTabs-indicator': {
              height: 2,
              backgroundColor: theme.palette.text.primary,
            },
          }}
        >
          <Tab label="Authentication Methods" />
          {showApiVersionsTab && <Tab label="API Versions" />}
         {showDataModelsTab &&<Tab label="Data Types" />}
        </Tabs>

        <Box sx={{ pt: 2 }}>
          <TabPanel value={tabValue} index={0}>
         {authMethods.length >= 2 && (
         <Box display="flex" alignItems="flex-start" gap={1} sx={{ mb: 2 }}>
        <Info size={16} color={theme.palette.info.main} style={{ marginTop: 3 }}
    />
    <Typography variant="body2" color="text.secondary">
      Unizo supports {authMethods.length} {serviceProfile?.name || 'Service'} authentication methods whichever you want to configure will be available in both API and Connect UI, allowing customers to configure integrations with ease.
    </Typography>
  </Box>
)}
           <Grid container spacing={2}>
              {authMethods.map((method) => (
                <Grid item xs={12} md={6} key={method.id}>
                  <Card
                    elevation={0}
                    sx={{
                      height: '100%',
                      borderRadius: 1,
                      backgroundColor: theme.palette.background.paper,
                      '&:hover': {
                        backgroundColor: theme.palette.mode === 'dark'
                          ? theme.palette.grey[800]
                          : theme.palette.grey[50],
                      },
                    }}
                  >
                    <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                      <Stack spacing={1.5}>
                        <Stack direction="row" spacing={1.5} alignItems="flex-start">
                          <Box sx={{ color: theme.palette.text.secondary, mt: 0.25 }}>
                            {method.icon}
                          </Box>
                          <Box flex={1}>
                            <Stack direction="row" alignItems="center" justifyContent="space-between">
                              <Typography variant="body2" fontWeight={500}>
                                {method.label}
                              </Typography>
                              {method.configured && isServiceActive && (
                                <Tooltip title="Authentication method is configured and ready to use">
                                  <CheckCircle size={16} color={theme.palette.success.main} />
                                </Tooltip>
                              )}
                            </Stack>
                            {method.description && (
                              <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 0.5 }}>
                                {method.description}
                              </Typography>
                            )}
                          </Box>
                        </Stack>

                        <Stack direction="row" alignItems="center" justifyContent="flex-end">
                          {!method.configured && (
                            <Button
                              size="small"
                              variant="text"
                              endIcon={<ChevronRight size={14} />}
                              onClick={() => {
                                if (method.type === AccessPointConfigType.AppFlow || method.type === AccessPointConfigType.OAuthFlow) {
                                  const accessPoint = accessPoints.find(ap => ap.id === method.id);
                                  if (accessPoint) {
                                    setSelectedTypeConfig(accessPoint);
                                  }
                                } else {
                                  onConfigureAuth?.(method);
                                }
                              }}
                              sx={{
                                textTransform: 'none',
                                fontSize: '0.75rem',
                                color: theme.palette.primary.main,
                                '&:hover': {
                                  backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                },
                              }}
                            >
                              Configure
                            </Button>
                          )}
                          {method.configured && (method.type === AccessPointConfigType.AppFlow || method.type === AccessPointConfigType.OAuthFlow) && (
                            <Button
                              size="small"
                              variant="outlined"
                              endIcon={<Settings size={14} />}
                              onClick={() => {
                                const accessPoint = accessPoints.find(ap => ap.id === method.id);
                                if (accessPoint) {
                                  setSelectedTypeConfig(accessPoint);
                                }
                              }}
                              sx={{
                                textTransform: 'none',
                                fontSize: '0.75rem',
                                borderColor: theme.palette.primary.main,
                                color: theme.palette.primary.main,
                                '&:hover': {
                                  borderColor: theme.palette.primary.dark,
                                  backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                },
                              }}
                            >
                              Configure
                            </Button>
                          )}
                        </Stack>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </TabPanel>

          {/* API Versions Tab */}
          {showApiVersionsTab && (
            <TabPanel value={tabValue} index={1}>
              <Stack spacing={2}>
                {authMethods.filter(m => m.configured && m.features?.version).map((method) => (
                  <Box key={method.id}>
                    <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1}>
                      <Stack direction="row" spacing={1.5} alignItems="center">
                        <Box sx={{ color: theme.palette.text.secondary }}>
                          {method.icon}
                        </Box>
                        <Typography variant="body2" fontWeight={500}>
                          {method.label}
                        </Typography>
                      </Stack>
                      <Chip
                        label={method.features?.version}
                        size="small"
                        variant="outlined"
                        sx={{
                          height: 22,
                          fontSize: '0.75rem',
                          borderColor: theme.palette.divider,
                        }}
                      />
                    </Stack>
                    {method.id !== authMethods.filter(m => m.configured && m.features?.version)[authMethods.filter(m => m.configured && m.features?.version).length - 1].id && (
                      <Divider sx={{ mt: 2 }} />
                    )}
                  </Box>
                ))}
              </Stack>
            </TabPanel>
          )}

          {/* Data Models Tab */}
  {showDataModelsTab && (
  <TabPanel value={tabValue} index={showApiVersionsTab ? 2 : 1}>
    <DataModelsConfiguration serviceProfile={serviceProfile} />
  </TabPanel>
)}
        </Box>
      </Box>

    </Box>
  );
}