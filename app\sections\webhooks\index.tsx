import { useState, useCallback, useMemo } from 'react';
import { 
  Ty<PERSON>graphy, 
  Button, 
  Box,
  Grid,
  Stack,
  IconButton,
  Menu,
  MenuItem,
  alpha,
  useTheme
} from '@mui/material';
import { MoreVertical, Webhook, Plus } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import FilterTable from 'components/@extended/Table/filter-table';
import { useGetOrganization } from 'hooks/api/organization/useGetOrganization';
import useUserDetails from 'store/user';
import { useServiceProfile } from 'hooks/useServiceProfile';
import Create from 'sections/services/setup/bi-directional/create';
import PageCard from 'components/cards/PageCard';
import LearnMoreLink from 'components/@extended/LearnMoreLink';

const CUSTOM_OPTIONS = [{ label: 'Platform', value: 'PLATFORM_WATCH_HOOK', key: 'PLATFORM' }];

const WebhooksPage = () => {
  const theme = useTheme();
  const { user, subscriptions: subscriptionsData } = useUserDetails();
  const { getAllDomains } = useServiceProfile();
  const { configs, isConfigLoading } = useGetOrganization({ id: user?.organization?.id });
  
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [selected, setSelected] = useState<Record<string, any> | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedWebhook, setSelectedWebhook] = useState<any>(null);
  const [paginationState, setPaginationState] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const data = configs?.data ?? [];
  const hasWebhooks = data.length > 0;

  const getHookTypeLabel = useCallback((hook: string) => {
    const extra = {
      name: 'Platform',
      value: 'PLATFORM',
      label: 'Platform',
      key: 'PLATFORM',
      hook: 'PLATFORM_WATCH_HOOK',
      released: false,
      getUpgraded: function () {
        return this.externalKey[this.externalKey.length - 1];
      },
      externalKey: ['PLATFORM_WATCH_HOOK', 'PLATFORM_WATCH_2_WAY_HOOK'],
      color: 'teal',
      description: 'Core platform services and integration capabilities supporting other modules.'
    };
  
    const all = [...(getAllDomains() ?? []), extra];
    return all.find((i) => i?.hook === hook)?.label ?? '';
  }, [getAllDomains]);

  const getOptions = useCallback(() => {
    const options = getAllDomains()
      .map(({ hook: value, key, label }) => ({ label, value, key }));

    const filteredOptions = options.filter(option =>
      subscriptionsData.map(({ product }) => product.code?.toUpperCase()).includes(option?.key)
    );
    
    return filteredOptions.concat(CUSTOM_OPTIONS).filter(({ value, ...rest }: any) => {
      function extractProperties<T extends Record<string, any>>(arr: Array<T>, property: keyof T): Array<T[keyof T]> {
        return arr.reduce((acc: Array<T[keyof T]>, cur) => {
          if (cur && property in cur) {
            acc.push(cur[property]);
          }
          return acc;
        }, []);
      }
      
      return (
        !extractProperties(data, 'type')?.includes(value) &&
        !rest.disabled
      );
    });
  }, [data, subscriptionsData, getAllDomains]);

  const onOpen = (
    arg: boolean,
    mode: boolean = false,
    selected: Record<string, any> | null = null
  ) => {
    setIsOpen(arg);
    setIsEditMode(mode);
    mode && setSelected(selected);
  };

  const onClose = () => {
    setIsOpen(false);
    setSelected(null);
    setIsEditMode(false);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, webhook: any) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedWebhook(webhook);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedWebhook(null);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    const now = new Date();
    
    const dateOptions: Intl.DateTimeFormatOptions = {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
    };
    
    const timeOptions: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    };
    
    const formattedDate = new Intl.DateTimeFormat(navigator.language || 'en-US', dateOptions).format(date);
    const formattedTime = new Intl.DateTimeFormat(navigator.language || 'en-US', timeOptions).format(date);
    
    const isToday = date.toDateString() === now.toDateString();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();
    
    if (isToday) {
      return `Today, ${formattedTime}`;
    } else if (isYesterday) {
      return `Yesterday, ${formattedTime}`;
    } else {
      return `${formattedDate}, ${formattedTime}`;
    }
  };

  const columns = useMemo<ColumnDef<any>[]>(() => [
    {
      header: () => <Typography variant="body2" sx={{ color: 'text.secondary', fontWeight: 600, textTransform: 'uppercase', fontSize: '0.75rem', letterSpacing: '0.5px' }}>Category</Typography>,
      accessorKey: 'type',
      cell: ({ row }) => (
        <Typography variant="body2">
          {getHookTypeLabel(row.original.type)}
        </Typography>
      )
    },
    {
      header: () => <Typography variant="body2" sx={{ color: 'text.secondary', fontWeight: 600, textTransform: 'uppercase', fontSize: '0.75rem', letterSpacing: '0.5px' }}>Callback URL</Typography>,
      accessorKey: 'data.url',
      cell: ({ row }) => (
        <Typography
          variant="body2"
          sx={{
            fontFamily: 'monospace',
            fontSize: '0.875rem',
            color: (theme) => theme.palette.text.secondary
          }}
        >
          {row.original.data?.url}
        </Typography>
      )
    },
    {
      header: () => <Typography variant="body2" sx={{ color: 'text.secondary', fontWeight: 600, textTransform: 'uppercase', fontSize: '0.75rem', letterSpacing: '0.5px' }}>Last Updated</Typography>,
      accessorKey: 'changeLog.lastUpdatedDateTime',
      cell: ({ row }) => {
        return (
          <Typography
            variant="body2"
            sx={{
              fontSize: '0.875rem',
              color: (theme) => theme.palette.text.secondary
            }}
          >
            {formatDate(row.original.changeLog?.lastUpdatedDateTime)}
          </Typography>
        )
      }
    },
    {
      header: () => <Typography variant="body2" sx={{ color: 'text.secondary', fontWeight: 600, textTransform: 'uppercase', fontSize: '0.75rem', letterSpacing: '0.5px' }}>Actions</Typography>,
      id: 'actions',
      cell: ({ row }) => (
        <IconButton
          size="small"
          onClick={(e) => handleMenuClick(e, row.original)}
          sx={{ color: 'text.secondary' }}
        >
          <MoreVertical size={20} />
        </IconButton>
      ),
      enableColumnFilter: false,
      enableSorting: false
    }
  ], [getHookTypeLabel]);

  // Show loader while data is being fetched
  if (isConfigLoading) {
    return (
      <PageCard>
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          minHeight: 400,
          flexDirection: 'column',
          gap: 2
        }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              border: `3px solid ${theme.palette.divider}`,
              borderTopColor: theme.palette.primary.main,
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' }
              }
            }}
          />
          <Typography variant="body2" color="text.secondary">
            Loading webhooks...
          </Typography>
        </Box>
      </PageCard>
    );
  }

  return (
    <>
      <Stack spacing={3}>
        {!hasWebhooks ? (
          // Empty state when no webhooks exist
          <PageCard>
            <Box 
              sx={{ 
                textAlign: 'center', 
                py: { xs: 6, sm: 8, md: 10 },
                px: { xs: 2, sm: 4 },
                maxWidth: 600,
                mx: 'auto'
              }}
            >
              <Box
                sx={{
                  width: { xs: 60, sm: 80 },
                  height: { xs: 60, sm: 80 },
                  borderRadius: '50%',
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mx: 'auto',
                  mb: 3
                }}
              >
                <Webhook 
                  size={40} 
                  style={{ 
                    color: theme.palette.primary.main 
                  }} 
                />
              </Box>
              
              <Typography variant="h5" fontWeight={600} gutterBottom>
                Configure your first webhook
              </Typography>
              
              <Typography 
                variant="body1" 
                color="text.secondary" 
                sx={{ mb: 4, maxWidth: 450, mx: 'auto' }}
              >
                Set up webhooks to receive real-time notifications about events in your integrations. Get instant updates when your users connect services or when data changes.
              </Typography>
              
              <Button
                variant="contained"
                size="large"
                startIcon={<Plus />}
                onClick={() => onOpen(true, false, null)}
                sx={{ 
                  px: 4,
                  textTransform: 'none',
                  fontWeight: 500
                }}
              >
                Add Webhook
              </Button>
              
              <Box sx={{ mt: 4 }}>
                <Typography variant="body2" color="text.secondary">
                  Learn more about webhook events{' '}
                  <LearnMoreLink
                    href="https://docs.unizo.ai/docs/unizo-console/webhooks"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    View documentation
                  </LearnMoreLink>
                </Typography>
              </Box>
            </Box>
          </PageCard>
        ) : (
          // Normal view when webhooks exist
          <>
            {/* Sub-header with button */}
            <Grid container justifyContent="space-between" alignItems="center">
              <Grid item>
              </Grid>
              <Grid item>
                <Button
                  variant="contained"
                  onClick={() => onOpen(true, false, null)}
                  sx={{
                    textTransform: 'none',
                    fontWeight: 500,
                  }}
                >
                  Add Webhook
                </Button>
              </Grid>
            </Grid>

            {/* Card-based Layout */}
            <Grid container spacing={2} sx={{ '& > .MuiGrid-item': { paddingLeft: 0 } }}>
              {data.map((webhook) => {
                const categoryName = getHookTypeLabel(webhook.type);
                  
                return (
                  <Grid item xs={12} key={webhook.id}>
                    <Box
                      sx={{
                        backgroundColor: theme.palette.background.paper,
                        borderRadius: 2,
                        border: `1px solid ${theme.palette.divider}`,
                        p: 2.5,
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          boxShadow: theme.shadows[1],
                          borderColor: theme.palette.primary.main,
                        }
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                        {/* Category with Icon */}
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: '0 0 auto', minWidth: 250 }}>
                          <Box
                            sx={{
                              width: 40,
                              height: 40,
                              borderRadius: 1.5,
                              backgroundColor: theme.palette.mode === 'dark' 
                                ? alpha(theme.palette.common.white, 0.05)
                                : theme.palette.grey[100],
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              flexShrink: 0
                            }}
                          >
                            <Webhook size={20} style={{ color: theme.palette.text.secondary }} />
                          </Box>
                          <Box>
                            <Typography 
                              variant="body1" 
                              sx={{ 
                                fontWeight: 600,
                                color: theme.palette.text.primary,
                                lineHeight: 1.2
                              }}
                            >
                              {categoryName}
                            </Typography>
                          </Box>
                        </Box>
                        
                        {/* Endpoint */}
                        <Box sx={{ flex: 1, minWidth: 0 }}>
                          <Typography 
                            variant="caption" 
                            sx={{ 
                              color: theme.palette.text.secondary,
                              fontWeight: 500,
                              textTransform: 'uppercase',
                              letterSpacing: '0.5px',
                              fontSize: '0.65rem',
                              display: 'block',
                              mb: 0.5
                            }}
                          >
                            Endpoint
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: 'monospace',
                              fontSize: '0.813rem',
                              color: theme.palette.text.primary,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap'
                            }}
                          >
                            {webhook.data?.url}
                          </Typography>
                        </Box>
                        
                        {/* Last Updated */}
                        <Box sx={{ flex: '0 0 auto', textAlign: 'right', minWidth: 150 }}>
                          <Typography 
                            variant="caption" 
                            sx={{ 
                              color: theme.palette.text.secondary,
                              fontWeight: 500,
                              textTransform: 'uppercase',
                              letterSpacing: '0.5px',
                              fontSize: '0.65rem',
                              display: 'block',
                              mb: 0.5
                            }}
                          >
                            Last Updated
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              fontSize: '0.813rem',
                              color: theme.palette.text.primary
                            }}
                          >
                            {formatDate(webhook.changeLog?.lastUpdatedDateTime)}
                          </Typography>
                        </Box>
                        
                        {/* Actions */}
                        <Box sx={{ flex: '0 0 auto' }}>
                          <IconButton
                            size="small"
                            onClick={(e) => handleMenuClick(e, webhook)}
                            sx={{ 
                              color: theme.palette.text.secondary,
                              '&:hover': {
                                backgroundColor: alpha(theme.palette.action.active, 0.08),
                              }
                            }}
                          >
                            <MoreVertical size={18} />
                          </IconButton>
                        </Box>
                      </Box>
                    </Box>
                  </Grid>
                );
              })}
              </Grid>
          </>
        )}
      </Stack>

      <Create 
        isEditMode={isEditMode} 
        selected={selected} 
        isOpen={isOpen} 
        onClose={onClose} 
        options={getOptions()} 
      />
      
      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          elevation: 3,
          sx: {
            mt: 1,
            minWidth: 120,
          }
        }}
      >
        <MenuItem onClick={() => {
          if (selectedWebhook) {
            onOpen(true, true, {
              ...selectedWebhook,
              name: getHookTypeLabel(selectedWebhook.type)
            });
          }
          handleMenuClose();
        }}>
          Edit
        </MenuItem>
        {/* <MenuItem onClick={() => {
          // Handle delete
          handleMenuClose();
        }}>
          Delete
        </MenuItem> */}
      </Menu>
    </>
  );
};

export default WebhooksPage;