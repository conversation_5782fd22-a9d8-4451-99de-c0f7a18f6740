// assets
import {
   Plug,
   GitMerge,
   Package,
   Settings,
   Key,
   Shield,
   Bell,
   Lock,
   Network,
   Boxes,
   Layers
} from 'lucide-react';

import { UserRoleEnum } from "hooks/api/permission/usePermission";
// icons
const icons = {
   ApiOutlined: Plug,
   MergeOutlined: GitMerge,
   ProductOutlined: Package,
   SettingOutlined: Settings,
   KeyOutlined: Key,
   SafetyOutlined: Shield,
   NotificationOutlined: Bell,
   LockOutlined: Lock,
   NodeExpandOutlined: Network,
   ClusterOutlined: Boxes
};
// ==============================|| MENU ITEMS - EXTRA PAGES ||============================== //
export default {
   id: 'configuration',
   title: 'Configure',
   type: 'group',
   children: [
      {
         id: 'set_integrations',
         title: 'Setup Integrations',
         description: 'Manage and supervise the integrations you offer to your users.',
         type: 'item',
         url: '/console/setup-integrations',
         icon: icons.ProductOutlined,
         hideFor: [UserRoleEnum.ORG_OBSERVER],
         hideForSuperAdmin: true
      },
      {
         id: 'webhooks',
         title: 'Webhooks',
         description: 'Configure webhooks to receive real-time updates.',
         type: 'item',
         url: '/console/webhooks',
         icon: icons.ApiOutlined,
         hideFor: [UserRoleEnum.ORG_OBSERVER],
         hideForSuperAdmin: true
      },
      {
         id: 'security',
         title: 'Security',
         description: 'Safeguards sensitive information, maintain data integrity, and enable continuous monitoring tools to detect and mitigate potential risks.',
         type: 'item',
         url: '/console/security',
         icon: icons.SafetyOutlined,
      },
      {
         id: 'alerts_notifications',
         title: 'Alerts & Notifications',
         description: '',
         type: 'item',
         url: '/console/alerts-notifications',
         disabled: true,
         icon: icons.NotificationOutlined,
      },
      {
         id: 'dock',
         title: 'Connect UI',
         description: "Unizo's Connect UI configuration allows you to deliver an in-browser docking experience and create integrations without any frontend code.",
         type: 'item',
         url: '/console/connect-UI',
         icon: Layers,
         hideFor: [UserRoleEnum.ORG_OBSERVER]
      },
      {
         id: 'api_key',
         title: 'API keys',
         description: 'Utilize the following API key to interact with our Unizo APIs, enabling seamless integration and enhanced functionality for your applications.',
         type: 'item',
         url: '/console/api-key',
         icon: icons.KeyOutlined,
         hideFor: [UserRoleEnum.ORG_OBSERVER]
      },
      {
         id: 'settings',
         title: 'Account Settings',
         description: 'Organize user accounts, determine roles and permissions, and look at your active subscription details.',
         type: 'item',
         url: '/console/settings',
         icon: icons.SettingOutlined,
         hideFor: [],
      },
      {
         id: 'environments',
         title: 'Environments',
         type: 'item',
         description: 'Manage your development, testing, and production setups using environments. Each environment is fully isolated, so changes in one won’t impact the others, giving you full control to build, test, and deploy safely.',
         url: '/console/environments',
         icon: icons.ClusterOutlined,
         hidden: true,
         hideFor: [],
       },
   ],
};


export const SUPER_ADMIN_EXCLUDES = [
   {
      id: 'set_integrations',
      title: 'Setup Integrations',
      type: 'item',
      url: '/console/setup-integrations',
      icon: icons.ProductOutlined,
      hideForSuperAdmin: true
   }
]