/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo, useState, useCallback, useEffect } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>er,
    TextField,
    IconButton,
    Divider,
    FormControl,
    Select,
    InputLabel,
    Chip,
    Alert,
    ToggleButton,
    ToggleButtonGroup,
    Box,
    useTheme,
    alpha,
    Menu,
    MenuItem,
    Tooltip,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useForm } from "react-hook-form";
import { useNavigate } from "@remix-run/react";
import { Form, FormField, FormItem } from "components/@extended/Form"
import Table from "components/@extended/Table";
import { useGetDockProfile } from "hooks/api/dockProfiles/useDockProfile";
import useUserDetails from "store/user";
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { EllipsisOutlined } from "@ant-design/icons";
import ConnectUIModal from "./ConnectUIModal";
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import AddIcon from '@mui/icons-material/Add';
import ViewListIcon from '@mui/icons-material/ViewList';
import ViewModuleIcon from '@mui/icons-material/ViewModule';
import MoreVert from '@mui/icons-material/MoreVert';
import Code from '@mui/icons-material/Code';
import WebAsset from '@mui/icons-material/WebAsset';
import nameChecker from "constants/categoryMapper";
import { State } from "hooks/useStatus";
import { serviceProfileClient } from "services/service-profile.service";
import { useServiceProfile } from "hooks/useServiceProfile";
import { useGetOrganization } from "hooks/api/organization/useGetOrganization";
import LearnMoreLink from "components/@extended/LearnMoreLink";
import ConfigurationCard from "./ConfigurationCard";

const TITLE = `Test run the authentication experience for your end users before integrating into your product.`;
const WARN_MESSAGE = (
  <>
    Configure a Platform webhook event listener to receive Integration related events.{" "}
    <LearnMoreLink
      href="https://docs.unizo.ai/docs/unizo-console/connect-ui/#webhook-integration"
      target="_blank"
      rel="noopener noreferrer"
    >
      Learn more
    </LearnMoreLink>
  </>
);

const defaultValues: WatchFormValues = {
    type: [] as any,
    customerKey: "",
    customerOrgName: "",
    provider: [] as any,
};

const alphaNumRegex = /^[a-zA-Z0-9-_]+$/;

const watchFormScheme = z.object({
    type: z
        .union([z.string(), z.array(z.string())])
        .optional()
        .transform(val => (typeof val === "string" ? [val] : val)),

    customerKey: z
        .string()
        .min(3, "Customer key must be at least 3 characters long")
        .max(36, "Customer key cannot exceed 36 characters")
        .regex(alphaNumRegex, "Key should be alphanumeric and may include hyphens and underscores only.")
        .nonempty("Customer key is required"),

    customerOrgName: z
        .string()
        .min(2, "Customer name must be at least 2 characters long")
        .max(15, "Customer name cannot exceed 15 characters")
        .regex(alphaNumRegex, "Name should be alphanumeric and may include hyphens and underscores only.")
        .nonempty("Customer name is required"),

    provider: z
        .union([z.string(), z.array(z.string())])
        .optional()
        .transform(val => (typeof val === "string" ? [val] : val)),
});

type WatchFormValues = z.infer<typeof watchFormScheme>

export default function ConnectUI() {
    const theme = useTheme();
    const navigate = useNavigate();
    const [selectedConfig, setSelectedConfig] = useState<any>(null);
    const [openDrawer, setOpenDrawer] = useState(false);
    const [openModal, setOpenModal] = useState(false);
    const [type, setType] = useState("category");
    const [viewMode, setViewMode] = useState<'card' | 'list'>('card');
    const [data, setData] = useState<any>()
    const [availableProviders, setAvailableProviders] = useState<any[]>([])
    const [userFlow, setUserFlow] = useState('')

    const { getDockProfiles, attemptDeleteDockProfile, attemptCreateServiceKey } = useGetDockProfile()
    const { categories, subscriptions: subscriptionsData, user } = useUserDetails()
    const { loadImage } = useServiceProfile();
    const { configs } = useGetOrganization({ id: user?.organization?.id || window?.authUserOrgId })

    const form = useForm<WatchFormValues>({
        resolver: zodResolver(watchFormScheme),
        defaultValues,
    });
    
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        watch,
        getValues,
        reset
    } = form;

    const webhookData: any = configs?.data || []
    const platformWebhook = webhookData?.find((webhook: { type: string }) => webhook.type === 'PLATFORM_WATCH_HOOK');
    const dockProfilesQuery = getDockProfiles()
    const { data: profileData, refetch, isLoading } = dockProfilesQuery
    
    // The hook returns the full response object with pagination and data
    const datas = Array.isArray(profileData?.data) ? profileData.data : []
    
    
    const columns = useMemo(
        () => [
            {
                header: "Name",
                accessorKey: "name",
                align: "left",
                cell({ value }: any) {
                    return (
                        <Typography variant="body2" fontWeight={500}>
                            {value}
                        </Typography>
                    );
                }
            },
            {
                header: "Custom Domain",
                accessorKey: "customDomain",
                align: "left",
                cell({ row }: any) {
                    const { customDomain } = row.original;
                    const hasCustomDomain = customDomain && customDomain !== 'https://dock.unizo.ai';
                    
                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Box
                                sx={{
                                    width: 8,
                                    height: 8,
                                    borderRadius: '50%',
                                    backgroundColor: hasCustomDomain 
                                        ? theme.palette.success.main 
                                        : theme.palette.grey[400]
                                }}
                            />
                            <Typography variant="body2" color={hasCustomDomain ? 'text.primary' : 'text.secondary'}>
                                {hasCustomDomain ? 'Enabled' : 'Disabled'}
                            </Typography>
                        </Box>
                    );
                }
            },
            {
                header: "Layout Type",
                accessorKey: "pageLayout",
                align: "left",
                cell({ value }: any) {
                    const isEmbedded = value === 'EMBEDDED';
                    const label = isEmbedded ? 'Embedded' : value === 'POP_UP' ? 'Pop-up' : value;
                    const icon = isEmbedded ? <Code sx={{ fontSize: 16 }} /> : <WebAsset sx={{ fontSize: 16 }} />;
                    
                    return (
                        <Chip
                            label={label}
                            icon={icon}
                            size="small"
                            sx={{ 
                                backgroundColor: theme.palette.mode === 'dark' 
                                    ? alpha(theme.palette.grey[700], 0.5)
                                    : theme.palette.grey[100],
                                color: theme.palette.text.primary,
                                border: `1px solid ${theme.palette.divider}`,
                                fontWeight: 400,
                                '& .MuiChip-icon': {
                                    color: theme.palette.text.secondary,
                                }
                            }}
                        />
                    )
                }
            },
            {
                header: "Actions",
                accessorKey: "action",
                align: "right",
                width: 150,
                cell({ row }: any) {
                    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
                    const open = Boolean(anchorEl);
                    
                    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
                        setAnchorEl(event.currentTarget);
                    };
                    
                    const handleClose = () => {
                        setAnchorEl(null);
                    };

                    return (
                        <Stack direction="row" spacing={0.5} justifyContent="flex-end">
                            <Tooltip title="Test Run">
                                <IconButton
                                    size="small"
                                    onClick={() => handleTestRun(row.original)}
                                    sx={{ 
                                        color: theme.palette.text.secondary,
                                        '&:hover': {
                                            backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                            color: theme.palette.primary.main,
                                        }
                                    }}
                                >
                                    <PlayArrowIcon fontSize="small" />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title="Edit">
                                <IconButton
                                    size="small"
                                    onClick={() => navigate(`/console/connect-UI/configuration/edit/${row.original.id}`)}
                                    sx={{ 
                                        color: theme.palette.text.secondary,
                                        '&:hover': {
                                            backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                            color: theme.palette.primary.main,
                                        }
                                    }}
                                >
                                    <EditIcon fontSize="small" />
                                </IconButton>
                            </Tooltip>
                            <IconButton
                                size="small"
                                aria-controls={open ? "actions-menu" : undefined}
                                aria-haspopup="true"
                                aria-expanded={open ? "true" : undefined}
                                onClick={handleClick}
                                sx={{ 
                                    color: theme.palette.text.secondary,
                                    '&:hover': {
                                        backgroundColor: alpha(theme.palette.action.active, 0.08),
                                    }
                                }}
                            >
                                <MoreVert fontSize="small" />
                            </IconButton>
                            <Menu
                                id="actions-menu"
                                anchorEl={anchorEl}
                                open={open}
                                onClose={handleClose}
                                anchorOrigin={{
                                    vertical: 'bottom',
                                    horizontal: 'right',
                                }}
                                transformOrigin={{
                                    vertical: 'top',
                                    horizontal: 'right',
                                }}
                                PaperProps={{
                                    sx: {
                                        mt: 0.5,
                                        minWidth: 120,
                                        boxShadow: theme.shadows[3],
                                    }
                                }}
                            >
                                <MenuItem
                                    onClick={() => {
                                        onDelete(row.original.id);
                                        handleClose();
                                    }}
                                    sx={{ 
                                        fontSize: '0.875rem',
                                        color: theme.palette.error.main,
                                        '&:hover': {
                                            backgroundColor: alpha(theme.palette.error.main, 0.08),
                                        }
                                    }}
                                >
                                    <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
                                    Delete
                                </MenuItem>
                            </Menu>
                        </Stack>
                    );
                },
            },
        ],
        []
    );

    // Load service profiles when type changes
    const getService = useCallback(async () => {
        const values = form.getValues();
        
        // Only proceed if there are types selected and provider mode
        if (!values.type || values.type.length === 0 || type !== "provider") return;
        
        const newPayload = {
            filter: {
                and: [
                    {
                        property: "/state",
                        operator: "=",
                        values: [State.ACTIVE],
                    },
                    {
                        property: "/organization/id",
                        operator: "=",
                        values: [user?.organization?.id],
                    },
                    {
                        property: "/type",
                        operator: "=",
                        values: [values.type],
                    },
                ]
            },
            paginationService: {
                limit: 33,
                offset: 0
            }
        };
        
        try {
            const response = await serviceProfileClient.searchServices(newPayload);
            setAvailableProviders(response.data.data || []);
        } catch (error) {
            console.error('Error loading service profiles:', error);
        }
    }, [form, type, user?.organization?.id]);
    
    useEffect(() => {
        if (type === "provider") {
            getService();
        }
    }, [watch("type"), type, getService])

    const onDelete = async (id: string) => {
        await attemptDeleteDockProfile(id);
        refetch();
    }

    const handleTestRun = (config: any) => {
        setSelectedConfig(config);
        setOpenDrawer(true);
        setUserFlow(config.userFlow?.type || 'CATEGORY');
        reset(defaultValues);
    };
    
    const getOptions = useCallback(() => {
        return subscriptionsData?.map(({ product }: any) => ({
            value: product?.code,
            label: product?.name,
        })) || [];
    }, [subscriptionsData]);
    
    const getProviderOptions = useCallback(() => {
        if (!availableProviders.length) return [];
        
        return availableProviders.map((service: any) => ({
            value: service.id,
            label: service.name,
            serviceProfile: service.serviceProfile
        }));
    }, [availableProviders]);

    const onSubmit = async (data: WatchFormValues) => {
        const { customerKey, customerOrgName, type: formType = [], provider = [] } = data;

        const payload = {
            type: "INTEGRATION_TOKEN",
            name: "organization-serviceKey",
            subOrganization: {
                name: customerOrgName,
                externalKey: customerKey
            },
            integration: {
                type: "GENERIC",
                target: userFlow === "PROVIDER" ?
                    {
                        type: "Provider",
                        providerSelectors: [
                            {
                                type: "SVC",
                                id: provider
                            }
                        ]
                    } :
                    {
                        type: "Category",
                        categorySelectors: Array.isArray(formType)
                            ? formType?.map((t) => ({ type: t })) || []
                            : [{ type: formType }]
                    }
            },
            dockProfile: {
                id: selectedConfig?.id
            }
        };
        
        attemptCreateServiceKey(payload, (data: any) => {
            const link = data?.data?.formDescriptorUrl;
            if (link) {
                setData(data);
                setOpenDrawer(false);
                setOpenModal(true);
            }
            reset(defaultValues);
        })
    }

    const handleCloseModal = () => {
        setData(undefined)
        setOpenModal(false);
        reset(defaultValues);
    }

    useEffect(() => {
        if (data) {
            setOpenDrawer(false)
            setOpenModal(true);
        }
    }, [data])

    const handleViewModeChange = (
        event: React.MouseEvent<HTMLElement>,
        newMode: 'card' | 'list' | null,
    ) => {
        if (newMode !== null) {
            setViewMode(newMode);
        }
    };

    return (
        <Stack spacing={3}>
            {/* Header */}
            <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography variant="h5" fontWeight={600}>
                    Configurations
                </Typography>
                <Stack direction="row" spacing={2}>
                    <ToggleButtonGroup
                        value={viewMode}
                        exclusive
                        onChange={handleViewModeChange}
                        aria-label="view mode"
                        size="small"
                    >
                        <ToggleButton value="card" aria-label="card view">
                            <ViewModuleIcon />
                        </ToggleButton>
                        <ToggleButton value="list" aria-label="list view">
                            <ViewListIcon />
                        </ToggleButton>
                    </ToggleButtonGroup>
                    <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={() => navigate("/console/connect-UI/configuration/create")}
                    >
                        Add Configuration
                    </Button>
                </Stack>
            </Stack>

            {/* Warning Alert */}
            {!platformWebhook && (
                <Alert severity="warning" sx={{ mx: 0 }}>
                    {WARN_MESSAGE}
                </Alert>
            )}

            {/* Configurations Section */}
            {isLoading ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                    <Typography color="text.secondary">Loading configurations...</Typography>
                </Box>
            ) : viewMode === 'card' ? (
                <Grid container spacing={2}>
                    {datas.map((config: any) => (
                        <Grid item xs={12} sm={6} md={4} lg={3} key={config.id}>
                            <ConfigurationCard
                                configuration={config}
                                onTestRun={handleTestRun}
                                onDelete={onDelete}
                            />
                        </Grid>
                    ))}
                    {datas.length === 0 && (
                        <Grid item xs={12}>
                            <Box
                                sx={{
                                    textAlign: 'center',
                                    py: 8,
                                    backgroundColor: alpha(theme.palette.grey[500], 0.04),
                                    borderRadius: 2,
                                    border: `1px dashed ${theme.palette.divider}`,
                                }}
                            >
                                <Typography variant="h6" color="text.secondary" gutterBottom>
                                    No configurations yet
                                </Typography>
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                                    Create your first Connect UI configuration to get started
                                </Typography>
                                <Button
                                    variant="contained"
                                    startIcon={<AddIcon />}
                                    onClick={() => navigate("/console/connect-UI/configuration/create")}
                                >
                                    Add Configuration
                                </Button>
                            </Box>
                        </Grid>
                    )}
                </Grid>
            ) : (
                datas.length === 0 ? (
                    <Box
                        sx={{
                            textAlign: 'center',
                            py: 8,
                            backgroundColor: alpha(theme.palette.grey[500], 0.04),
                            borderRadius: 2,
                            border: `1px dashed ${theme.palette.divider}`,
                        }}
                    >
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                            No configurations yet
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                            Create your first Connect UI configuration to get started
                        </Typography>
                        <Button
                            variant="contained"
                            startIcon={<AddIcon />}
                            onClick={() => navigate("/console/connect-UI/configuration/create")}
                        >
                            Add Configuration
                        </Button>
                    </Box>
                ) : (
                    <Table
                        data={datas}
                        columns={columns}
                        striped={false}
                        sx={{ 
                            mt: 2,
                            '& .MuiTableContainer-root': {
                                border: `1px solid ${theme.palette.divider}`,
                                borderRadius: 2,
                                overflow: 'hidden',
                            },
                            '& .MuiTableCell-root': {
                                borderBottom: `1px solid ${theme.palette.divider}`,
                                py: 2,
                            },
                            '& .MuiTableRow-root:last-child .MuiTableCell-root': {
                                borderBottom: 'none',
                            },
                            '& .MuiTableHead-root': {
                                '& .MuiTableCell-root': {
                                    backgroundColor: theme.palette.mode === 'dark' 
                                        ? alpha(theme.palette.grey[900], 0.5)
                                        : theme.palette.grey[50],
                                    fontWeight: 600,
                                    fontSize: '0.75rem',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.05em',
                                    color: theme.palette.text.secondary,
                                }
                            },
                            '& .MuiTableBody-root .MuiTableRow-root': {
                                '&:hover': {
                                    backgroundColor: alpha(theme.palette.action.hover, 0.03),
                                }
                            }
                        }}
                    />
                )
            )}

            {/* Test Run Drawer */}
            <Drawer
                anchor="right"
                open={openDrawer}
                onClose={(event, reason) => {
                    if (reason === "backdropClick") return;
                    setOpenDrawer(false);
                }}
                sx={{
                    '& .MuiDrawer-paper': {
                        width: 400,
                        p: 3,
                    },
                }}
            >
                <Stack spacing={3}>
                    <Stack direction="row" sx={{ justifyContent: "space-between", alignItems: "center" }}>
                        <Typography variant="h6" fontWeight={600}>
                            Test Run Configuration
                        </Typography>
                        <IconButton onClick={() => setOpenDrawer(false)}>
                            <CloseIcon />
                        </IconButton>
                    </Stack>
                    
                    <Divider />
                    
                    <Typography variant="body2" color="text.secondary">
                        The Generated URL is for testing purposes only. Use the Service Key API to integrate it into your product.{" "}
                        <LearnMoreLink href="https://docs.unizo.ai/docs/api/platform-reference/create-service-key/" target="_blank" rel="noopener noreferrer">
                            Learn more
                        </LearnMoreLink>
                    </Typography>
                    
                    <Form onSubmit={handleSubmit(onSubmit)}>
                        <Stack spacing={3}>
                            <FormField
                                control={control}
                                name="customerKey"
                                render={({ field }) => (
                                    <FormItem>
                                        <TextField
                                            {...field}
                                            label="Customer Key"
                                            placeholder="Enter customer key"
                                            fullWidth
                                            error={!!errors.customerKey}
                                            helperText={errors.customerKey?.message}
                                        />
                                    </FormItem>
                                )}
                            />
                            
                            <FormField
                                control={control}
                                name="customerOrgName"
                                render={({ field }) => (
                                    <FormItem>
                                        <TextField
                                            {...field}
                                            label="Customer Name"
                                            placeholder="Enter customer name"
                                            fullWidth
                                            error={!!errors.customerOrgName}
                                            helperText={errors.customerOrgName?.message}
                                        />
                                    </FormItem>
                                )}
                            />
                            
                            {/* Category Selection */}
                            <FormField
                                control={control}
                                name="type"
                                render={({ field }) => {
                                    const provider = userFlow === "PROVIDER";
                                    const availableOptions = getOptions()?.filter(
                                        (option: any) => provider
                                            ? field.value !== option.value
                                            : !field.value?.includes(option.value)
                                    );

                                    return (
                                        <FormItem>
                                            <FormControl fullWidth>
                                                <InputLabel id="category-select-label">Select category</InputLabel>
                                                <Select
                                                    {...field}
                                                    labelId="category-select-label"
                                                    label="Select category"
                                                    multiple={!provider}
                                                    value={provider
                                                        ? (field.value || '')
                                                        : (Array.isArray(field.value) ? field.value : [])
                                                    }
                                                    onChange={e => {
                                                        field.onChange(e.target.value);
                                                        if (provider) {
                                                            getService();
                                                        }
                                                    }}
                                                    renderValue={(selected) => {
                                                        if (provider) {
                                                            const option = getOptions().find((opt: any) => opt.value === selected);
                                                            return option ? nameChecker(option.label) : "";
                                                        }
                                                        return Array.isArray(selected) && selected.length > 0 
                                                            ? selected.map(s => {
                                                                const opt = getOptions().find((o: any) => o.value === s);
                                                                return opt ? nameChecker(opt.label) : s;
                                                            }).join(', ')
                                                            : "";
                                                    }}
                                                >
                                                    {availableOptions?.map((domain: any) => (
                                                        <MenuItem key={domain?.value} value={domain?.value}>
                                                            {nameChecker(domain?.label)}
                                                        </MenuItem>
                                                    ))}
                                                </Select>
                                            </FormControl>
                                            {errors.type && (
                                                <Typography variant="caption" color="error">
                                                    {errors.type.message}
                                                </Typography>
                                            )}
                                            {/* Chips for multiple selection */}
                                            {!provider && Array.isArray(field.value) && field.value.length > 0 && (
                                                <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                                    {field.value.map((val: any) => {
                                                        const option = getOptions().find((opt: any) => opt.value === val);
                                                        return option ? (
                                                            <Chip
                                                                key={val}
                                                                label={nameChecker(option.label)}
                                                                size="small"
                                                                onDelete={() => {
                                                                    const newValues = field.value?.filter((item: any) => item !== val) || [];
                                                                    setValue("type", newValues);
                                                                }}
                                                            />
                                                        ) : null;
                                                    })}
                                                </Box>
                                            )}
                                        </FormItem>
                                    );
                                }}
                            />
                            
                            {/* Provider Selection (only shown if provider mode) */}
                            {userFlow === "PROVIDER" && (
                                <FormField
                                    control={control}
                                    name="provider"
                                    render={({ field }) => {
                                        const providerOptions = getProviderOptions();
                                        
                                        return (
                                            <FormItem>
                                                <FormControl fullWidth>
                                                    <InputLabel id="provider-select-label">Select service</InputLabel>
                                                    <Select
                                                        {...field}
                                                        labelId="provider-select-label"
                                                        label="Select service"
                                                        value={field.value || ''}
                                                        onChange={e => field.onChange(e.target.value)}
                                                        disabled={!watch('type')}
                                                        renderValue={(selected) => {
                                                            const option = providerOptions.find((opt: any) => opt.value === selected);
                                                            return option ? (
                                                                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                                    {loadImage(option?.serviceProfile, { size: 'xSmall' })}
                                                                    {option.label}
                                                                </Box>
                                                            ) : "";
                                                        }}
                                                    >
                                                        {providerOptions.length > 0 ? (
                                                            providerOptions.map((providerItem: any) => (
                                                                <MenuItem key={providerItem.value} value={providerItem.value}>
                                                                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                                        {loadImage(providerItem?.serviceProfile, { size: "xSmall" })}
                                                                        {providerItem.label}
                                                                    </Box>
                                                                </MenuItem>
                                                            ))
                                                        ) : (
                                                            <MenuItem disabled>
                                                                <Typography>No connectors enabled</Typography>
                                                            </MenuItem>
                                                        )}
                                                    </Select>
                                                </FormControl>
                                                {errors.provider && (
                                                    <Typography variant="caption" color="error">
                                                        {errors.provider.message}
                                                    </Typography>
                                                )}
                                            </FormItem>
                                        );
                                    }}
                                />
                            )}

                            <Stack direction="row" spacing={2} sx={{ mt: 3 }}>
                                <Button
                                    variant="outlined"
                                    onClick={() => setOpenDrawer(false)}
                                    fullWidth
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    variant="contained"
                                    fullWidth
                                >
                                    Generate Test Link
                                </Button>
                            </Stack>
                        </Stack>
                    </Form>
                </Stack>
            </Drawer>

            {/* Modal for test URL */}
            {openModal && data && (
                <ConnectUIModal
                    opened={openModal}
                    onClosed={handleCloseModal}
                    magicLink={data?.data?.formDescriptorUrl}
                />
            )}
        </Stack>
    );
}