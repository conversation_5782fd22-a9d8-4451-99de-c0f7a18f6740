/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo, useState, useCallback, useEffect } from "react";
import {
    <PERSON>rid,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Drawer,
    TextField,
    IconButton,
    Divider,
    FormControl,
    Select,
    InputLabel,
    Chip,
    Alert,
    Box,
    useTheme,
    alpha,
    Menu,
    MenuItem,
    Tooltip,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useForm } from "react-hook-form";
import { useNavigate } from "@remix-run/react";
import { Form, FormField, FormItem } from "components/@extended/Form"
import Table from "components/@extended/Table";
import { useGetDockProfile } from "hooks/api/dockProfiles/useDockProfile";
import useUserDetails from "store/user";
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { EllipsisOutlined } from "@ant-design/icons";
import ConnectUIModal from "./ConnectUIModal";
import ConfigurationDrawer from "./ConfigurationDrawer";
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import AddIcon from '@mui/icons-material/Add';
import MoreVert from '@mui/icons-material/MoreVert';
import Code from '@mui/icons-material/Code';
import WebAsset from '@mui/icons-material/WebAsset';
import nameChecker from "constants/categoryMapper";
import { State } from "hooks/useStatus";
import { serviceProfileClient } from "services/service-profile.service";
import { useServiceProfile } from "hooks/useServiceProfile";
import { useGetOrganization } from "hooks/api/organization/useGetOrganization";
import LearnMoreLink from "components/@extended/LearnMoreLink";
import MainCard from "components/MainCard";
import PageCard from "components/cards/PageCard";
import { Layers, Plus } from 'lucide-react';

const TITLE = `Test run the authentication experience for your end users before integrating into your product.`;
const WARN_MESSAGE = (
  <>
    Configure a Platform webhook event listener to receive Integration related events.{" "}
    <LearnMoreLink
      href="https://docs.unizo.ai/docs/unizo-console/connect-ui/#webhook-integration"
      target="_blank"
      rel="noopener noreferrer"
    >
      Learn more
    </LearnMoreLink>
  </>
);

const defaultValues: WatchFormValues = {
    type: [] as any,
    customerKey: "",
    customerOrgName: "",
    provider: [] as any,
};

const alphaNumRegex = /^[a-zA-Z0-9-_]+$/;

const watchFormScheme = z.object({
    type: z
        .union([z.string(), z.array(z.string())])
        .optional()
        .transform(val => (typeof val === "string" ? [val] : val)),

    customerKey: z
        .string()
        .min(3, "Customer key must be at least 3 characters long")
        .max(36, "Customer key cannot exceed 36 characters")
        .regex(alphaNumRegex, "Key should be alphanumeric and may include hyphens and underscores only.")
        .nonempty("Customer key is required"),

    customerOrgName: z
        .string()
        .min(2, "Customer name must be at least 2 characters long")
        .max(15, "Customer name cannot exceed 15 characters")
        .regex(alphaNumRegex, "Name should be alphanumeric and may include hyphens and underscores only.")
        .nonempty("Customer name is required"),

    provider: z
        .union([z.string(), z.array(z.string())])
        .optional()
        .transform(val => (typeof val === "string" ? [val] : val)),
});

type WatchFormValues = z.infer<typeof watchFormScheme>

export default function ConnectUI() {
    const theme = useTheme();
    const navigate = useNavigate();
    const [selectedConfig, setSelectedConfig] = useState<any>(null);
    const [openDrawer, setOpenDrawer] = useState(false);
    const [openModal, setOpenModal] = useState(false);
    const [openConfigDrawer, setOpenConfigDrawer] = useState(false);
    const [editConfigId, setEditConfigId] = useState<string | null>(null);
    const [type, setType] = useState("category");
    const [data, setData] = useState<any>()
    const [availableProviders, setAvailableProviders] = useState<any[]>([])
    const [userFlow, setUserFlow] = useState('')
    const [paginationState, setPaginationState] = useState<any>({
        pageIndex: 0,
        pageSize: 10,
    })

    const { getDockProfiles, attemptDeleteDockProfile, attemptCreateServiceKey } = useGetDockProfile()
    const { categories, subscriptions: subscriptionsData, user } = useUserDetails()
    const { loadImage } = useServiceProfile();
    const { configs } = useGetOrganization({ id: user?.organization?.id || window?.authUserOrgId })

    const form = useForm<WatchFormValues>({
        resolver: zodResolver(watchFormScheme),
        defaultValues,
    });
    
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        watch,
        getValues,
        reset
    } = form;

    const webhookData: any = configs?.data || []
    const platformWebhook = webhookData?.find((webhook: { type: string }) => webhook.type === 'PLATFORM_WATCH_HOOK');
    const dockProfilesQuery = getDockProfiles()
    const { data: profileData, refetch, isLoading } = dockProfilesQuery
    
    // The hook returns the full response object with pagination and data
    const datas = Array.isArray(profileData?.data) ? profileData.data : []
    
    
    const updatedValues: Record<string, string> = {
        POP_UP: "Pop Up",
        EMBEDDED: "Embedded"
    };

    const columns = useMemo(
        () => [
            {
                accessorKey: 'name',
                header: 'Name',
                minSize: 150,
                size: 200,
                cell({ row: { original: { name, id } } }: any) {
                    return (
                        <Stack spacing={0.5}>
                            <Typography 
                                variant="body2" 
                                fontWeight={500}
                                sx={{
                                    fontSize: { xs: '0.813rem', sm: '0.875rem' },
                                    lineHeight: 1.4
                                }}
                            >
                                {name}
                            </Typography>
                            <Typography 
                                variant="caption" 
                                color="text.secondary"
                                sx={{ 
                                    wordBreak: 'break-word',
                                    fontSize: { xs: '0.688rem', sm: '0.75rem' },
                                    display: { xs: 'none', sm: 'block' }
                                }}
                            >
                                {id}
                            </Typography>
                        </Stack>
                    )
                }
            },
            {
                accessorKey: 'frontendUrl',
                header: () => (
                    <Typography sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                        Custom Domain
                    </Typography>
                ),
                cell({ row: { original: { frontendUrl } } }: any) {
                    const isCustomDomain = frontendUrl && frontendUrl.trim() !== 'https://dock.unizo.ai';
                    
                    if (!isCustomDomain) {
                        return (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, sm: 1 } }}>
                                <Box
                                    sx={{
                                        width: { xs: 6, sm: 8 },
                                        height: { xs: 6, sm: 8 },
                                        borderRadius: '50%',
                                        backgroundColor: theme.palette.grey[400],
                                        flexShrink: 0
                                    }}
                                />
                                <Typography 
                                    variant="body2" 
                                    color="text.secondary"
                                    sx={{ 
                                        fontSize: { xs: '0.75rem', sm: '0.875rem' },
                                        display: { xs: 'none', sm: 'block' }
                                    }}
                                >
                                    Not configured
                                </Typography>
                            </Box>
                        );
                    }
                    
                    // Extract domain from URL
                    let displayDomain = frontendUrl;
                    try {
                        const url = new URL(frontendUrl);
                        displayDomain = url.hostname;
                    } catch (e) {
                        // If not a valid URL, show as is
                    }
                    
                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, sm: 1 } }}>
                            <Box
                                sx={{
                                    width: { xs: 6, sm: 8 },
                                    height: { xs: 6, sm: 8 },
                                    borderRadius: '50%',
                                    backgroundColor: theme.palette.success.main,
                                    flexShrink: 0
                                }}
                            />
                            <Typography 
                                variant="body2"
                                sx={{ 
                                    fontSize: { xs: '0.75rem', sm: '0.875rem' },
                                    wordBreak: 'break-word',
                                    maxWidth: { xs: 100, sm: 150, md: 200, lg: 'none' }
                                }}
                            >
                                {displayDomain}
                            </Typography>
                        </Box>
                    );
                }
            },
            {
                accessorKey: 'pageLayout',
                header: () => (
                    <Typography sx={{ 
                        fontSize: { xs: '0.75rem', sm: '0.875rem' },
                        display: { xs: 'none', md: 'block' }
                    }}>
                        Layout Type
                    </Typography>
                ),
                enableHiding: true,
                cell({ row: { original: { pageLayout } } }: any) {
                    return (
                        <Typography 
                            variant="body2"
                            sx={{ 
                                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                                display: { xs: 'none', md: 'block' }
                            }}
                        >
                            {updatedValues[pageLayout] || pageLayout}
                        </Typography>
                    )
                }

            },
            {
                accessorKey: 'changeLog.lastUpdatedDateTime',
                header: () => (
                    <Typography sx={{ 
                        fontSize: { xs: '0.75rem', sm: '0.875rem' },
                        display: { xs: 'none', sm: 'block' }
                    }}>
                        Last Updated
                    </Typography>
                ),
                cell({ row: { original: { changeLog } } }: any) {
                    const formatDate = (dateString: string) => {
                        if (!dateString) return '-';
                        
                        const date = new Date(dateString);
                        const now = new Date();
                        
                        // Format date and time based on user's locale
                        const dateOptions: Intl.DateTimeFormatOptions = {
                            month: 'short',
                            day: 'numeric',
                            year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
                        };
                        
                        const timeOptions: Intl.DateTimeFormatOptions = {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true
                        };
                        
                        const formattedDate = new Intl.DateTimeFormat(navigator.language || 'en-US', dateOptions).format(date);
                        const formattedTime = new Intl.DateTimeFormat(navigator.language || 'en-US', timeOptions).format(date);
                        
                        // Check if it's today
                        const isToday = date.toDateString() === now.toDateString();
                        const yesterday = new Date(now);
                        yesterday.setDate(yesterday.getDate() - 1);
                        const isYesterday = date.toDateString() === yesterday.toDateString();
                        
                        if (isToday) {
                            return `Today, ${formattedTime}`;
                        } else if (isYesterday) {
                            return `Yesterday, ${formattedTime}`;
                        } else {
                            return `${formattedDate}, ${formattedTime}`;
                        }
                    };
                    
                    return (
                        <Typography 
                            variant="body2" 
                            color="text.secondary"
                            sx={{ 
                                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                                display: { xs: 'none', sm: 'block' }
                            }}
                        >
                            {formatDate(changeLog?.lastUpdatedDateTime)}
                        </Typography>
                    )
                }
            },
            {
                accessorKey: 'action',
                header: () => (
                    <Typography sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                        Actions
                    </Typography>
                ),
                size: 120,
                minSize: 100,
                cell({ row: { original } }: any) {
                    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
                    const open = Boolean(anchorEl);
                    
                    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
                        event.stopPropagation();
                        setAnchorEl(event.currentTarget);
                    };
                    
                    const handleClose = () => {
                        setAnchorEl(null);
                    };

                    return (
                        <Stack direction="row" spacing={0.25}>
                            <Tooltip title="Test Run">
                                <IconButton
                                    size="small"
                                    onClick={() => handleTestRun(original)}
                                    sx={{ 
                                        color: theme.palette.text.secondary,
                                        padding: { xs: '4px', sm: '8px' },
                                        '&:hover': {
                                            backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                            color: theme.palette.primary.main,
                                        }
                                    }}
                                >
                                    <PlayArrowIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title="Edit">
                                <IconButton
                                    size="small"
                                    onClick={() => {
                                        setEditConfigId(original.id);
                                        setOpenConfigDrawer(true);
                                    }}
                                    sx={{ 
                                        color: theme.palette.text.secondary,
                                        padding: { xs: '4px', sm: '8px' },
                                        display: { xs: 'none', sm: 'inline-flex' },
                                        '&:hover': {
                                            backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                            color: theme.palette.primary.main,
                                        }
                                    }}
                                >
                                    <EditIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />
                                </IconButton>
                            </Tooltip>
                            <IconButton
                                size="small"
                                onClick={handleClick}
                                sx={{ 
                                    color: theme.palette.text.secondary,
                                    padding: { xs: '4px', sm: '8px' },
                                    '&:hover': {
                                        backgroundColor: alpha(theme.palette.action.active, 0.08),
                                    }
                                }}
                            >
                                <MoreVert sx={{ fontSize: { xs: 18, sm: 20 } }} />
                            </IconButton>
                            <Menu
                                anchorEl={anchorEl}
                                open={open}
                                onClose={handleClose}
                                anchorOrigin={{
                                    vertical: 'bottom',
                                    horizontal: 'right',
                                }}
                                transformOrigin={{
                                    vertical: 'top',
                                    horizontal: 'right',
                                }}
                                PaperProps={{
                                    sx: {
                                        mt: 0.5,
                                        minWidth: 120,
                                        boxShadow: theme.shadows[3],
                                    }
                                }}
                            >
                                <MenuItem
                                    onClick={() => {
                                        setEditConfigId(original.id);
                                        setOpenConfigDrawer(true);
                                        handleClose();
                                    }}
                                    sx={{ 
                                        fontSize: '0.875rem',
                                        display: { xs: 'flex', sm: 'none' }
                                    }}
                                >
                                    <EditIcon fontSize="small" sx={{ mr: 1 }} />
                                    Edit
                                </MenuItem>
                                <MenuItem
                                    onClick={() => {
                                        onDelete(original.id);
                                        handleClose();
                                    }}
                                    sx={{ 
                                        fontSize: '0.875rem',
                                        color: theme.palette.error.main,
                                        '&:hover': {
                                            backgroundColor: alpha(theme.palette.error.main, 0.08),
                                        }
                                    }}
                                >
                                    <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
                                    Delete
                                </MenuItem>
                            </Menu>
                        </Stack>
                    );
                }
            },
        ],
        [],
    );

    // Load service profiles when type changes
    const getService = useCallback(async () => {
        const values = form.getValues();
        
        // Only proceed if there are types selected and provider mode
        if (!values.type || values.type.length === 0 || type !== "provider") return;
        
        const newPayload = {
            filter: {
                and: [
                    {
                        property: "/state",
                        operator: "=",
                        values: [State.ACTIVE],
                    },
                    {
                        property: "/organization/id",
                        operator: "=",
                        values: [user?.organization?.id],
                    },
                    {
                        property: "/type",
                        operator: "=",
                        values: [values.type],
                    },
                ]
            },
            paginationService: {
                limit: 33,
                offset: 0
            }
        };
        
        try {
            const response = await serviceProfileClient.searchServices(newPayload);
            setAvailableProviders(response.data.data || []);
        } catch (error) {
            console.error('Error loading service profiles:', error);
        }
    }, [form, type, user?.organization?.id]);
    
    useEffect(() => {
        if (type === "provider") {
            getService();
        }
    }, [watch("type"), type, getService])

    const onDelete = async (id: string) => {
        await attemptDeleteDockProfile(id);
        refetch();
    }


    const handleTestRun = (config: any) => {
        setSelectedConfig(config);
        setOpenDrawer(true);
        setUserFlow(config.userFlow?.type || 'CATEGORY');
        reset(defaultValues);
    };
    
    const getOptions = useCallback(() => {
        return subscriptionsData?.map(({ product }: any) => ({
            value: product?.code,
            label: product?.name,
        })) || [];
    }, [subscriptionsData]);
    
    const getProviderOptions = useCallback(() => {
        if (!availableProviders.length) return [];
        
        return availableProviders.map((service: any) => ({
            value: service.id,
            label: service.name,
            serviceProfile: service.serviceProfile
        }));
    }, [availableProviders]);

    const onSubmit = async (data: WatchFormValues) => {
        const { customerKey, customerOrgName, type: formType = [], provider = [] } = data;

        const payload = {
            type: "INTEGRATION_TOKEN",
            name: "organization-serviceKey",
            subOrganization: {
                name: customerOrgName,
                externalKey: customerKey
            },
            integration: {
                type: "GENERIC",
                target: userFlow === "PROVIDER" ?
                    {
                        type: "Provider",
                        providerSelectors: [
                            {
                                type: "SVC",
                                id: provider
                            }
                        ]
                    } :
                    {
                        type: "Category",
                        categorySelectors: Array.isArray(formType)
                            ? formType?.map((t) => ({ type: t })) || []
                            : [{ type: formType }]
                    }
            },
            dockProfile: {
                id: selectedConfig?.id
            }
        };
        
        attemptCreateServiceKey(payload, (data: any) => {
            const link = data?.data?.formDescriptorUrl;
            if (link) {
                setData(data);
                setOpenDrawer(false);
                setOpenModal(true);
            }
            reset(defaultValues);
        })
    }

    const handleCloseModal = () => {
        setData(undefined)
        setOpenModal(false);
        reset(defaultValues);
    }

    useEffect(() => {
        if (data) {
            setOpenDrawer(false)
            setOpenModal(true);
        }
    }, [data])


    const activeProfiles = datas.filter((item: any) => item.state === "ACTIVE");
    const hasActiveProfiles = activeProfiles.length > 0;

    // Show loader while data is being fetched
    if (isLoading) {
        return (
            <Stack spacing={3}>
                {!platformWebhook && (
                    <Alert severity="warning" sx={{textAlign:'justify'}}>
                        {WARN_MESSAGE} 
                    </Alert>
                )}
                <PageCard>
                    <Box sx={{ 
                        display: 'flex', 
                        justifyContent: 'center', 
                        alignItems: 'center',
                        minHeight: 400,
                        flexDirection: 'column',
                        gap: 2
                    }}>
                        <Box
                            sx={{
                                width: 40,
                                height: 40,
                                border: `3px solid ${theme.palette.divider}`,
                                borderTopColor: theme.palette.primary.main,
                                borderRadius: '50%',
                                animation: 'spin 1s linear infinite',
                                '@keyframes spin': {
                                    '0%': { transform: 'rotate(0deg)' },
                                    '100%': { transform: 'rotate(360deg)' }
                                }
                            }}
                        />
                        <Typography variant="body2" color="text.secondary">
                            Loading configurations...
                        </Typography>
                    </Box>
                </PageCard>
            </Stack>
        );
    }

    return (
        <Stack spacing={3}>
            {!platformWebhook && (
                <Alert severity="warning" sx={{textAlign:'justify'}}>
                    {WARN_MESSAGE} 
                </Alert>
            )}
            
            {!hasActiveProfiles ? (
                // Empty state when no configurations exist
                <PageCard>
                    <Box 
                        sx={{ 
                            textAlign: 'center', 
                            py: { xs: 6, sm: 8, md: 10 },
                            px: { xs: 2, sm: 4 },
                            maxWidth: 600,
                            mx: 'auto'
                        }}
                    >
                        <Box
                            sx={{
                                width: { xs: 60, sm: 80 },
                                height: { xs: 60, sm: 80 },
                                borderRadius: '50%',
                                backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.1),
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                mx: 'auto',
                                mb: 3
                            }}
                        >
                            <Layers 
                                size={40} 
                                style={{ 
                                    color: theme.palette.primary.main 
                                }} 
                            />
                        </Box>
                        
                        <Typography variant="h5" fontWeight={600} gutterBottom>
                            Create your first Connect UI
                        </Typography>
                        
                        <Typography 
                            variant="body1" 
                            color="text.secondary" 
                            sx={{ mb: 4, maxWidth: 450, mx: 'auto' }}
                        >
                            Set up pre-built authentication experiences for your end users. Test run the authentication flow before integrating into your product.
                        </Typography>
                        
                        <Button
                            variant="contained"
                            size="large"
                            startIcon={<Plus />}
                            onClick={() => {
                                setEditConfigId(null);
                                setOpenConfigDrawer(true);
                            }}
                            sx={{ 
                                px: 4,
                                textTransform: 'none',
                                fontWeight: 500
                            }}
                        >
                            New Connect UI
                        </Button>
                        
                        <Box sx={{ mt: 4 }}>
                            <Typography variant="body2" color="text.secondary">
                                Need help getting started?{' '}
                                <LearnMoreLink
                                    href="https://docs.unizo.ai/docs/unizo-console/connect-ui"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    View documentation
                                </LearnMoreLink>
                            </Typography>
                        </Box>
                    </Box>
                </PageCard>
            ) : (
                // Normal view when configurations exist
                <>
                    <MainCard>
                        <Grid container justifyContent="space-between" alignItems="center">
                            <Grid item xs={12} md={8}>
                                <Stack spacing={1}>
                                    <Typography variant="h6">{TITLE}</Typography>
                                </Stack>
                            </Grid>
                            <Grid item xs={12} md={4} sx={{ textAlign: { xs: 'left', md: 'right' }, mt: { xs: 2, md: 0 } }}>
                                <Button
                                    variant="contained"
                                    onClick={() => {
                                        setEditConfigId(null);
                                        setOpenConfigDrawer(true);
                                    }}
                                >
                                    New Connect UI
                                </Button>
                            </Grid>
                        </Grid>
                    </MainCard>
                    
                    <Box sx={{ 
                        overflowX: 'auto',
                        '& .MuiTableContainer-root': {
                            minWidth: { xs: 300, sm: 'auto' }
                        }
                    }}>
                        <Table
                            data={activeProfiles}
                            columns={columns}
                            totalData={activeProfiles.length}
                            {...{
                                onPaginationChange: setPaginationState,
                                state: {
                                    pagination: {
                                        pageIndex: paginationState.pageIndex,
                                        pageSize: paginationState?.pageSize,
                                    }
                                } as any,
                            }}
                            sx={{
                                '& .MuiTableCell-root': {
                                    padding: { xs: '8px 4px', sm: '16px 8px', md: '16px' },
                                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                                },
                                '& .MuiTableHead-root .MuiTableCell-root': {
                                    padding: { xs: '12px 4px', sm: '16px 8px', md: '16px' },
                                    fontWeight: 600
                                }
                            }}
                        />
                    </Box>
                </>
            )}

            {/* Test Run Drawer */}
            <Drawer
                anchor="right"
                open={openDrawer}
                onClose={(event, reason) => {
                    if (reason === "backdropClick") return;
                    setOpenDrawer(false);
                }}
                sx={{
                    '& .MuiDrawer-paper': {
                        width: 400,
                        p: 3,
                    },
                }}
            >
                <Stack spacing={3}>
                    <Stack direction="row" sx={{ justifyContent: "space-between", alignItems: "center" }}>
                        <Typography variant="h6" fontWeight={600}>
                            Test Run Configuration
                        </Typography>
                        <IconButton onClick={() => setOpenDrawer(false)}>
                            <CloseIcon />
                        </IconButton>
                    </Stack>
                    
                    <Divider />
                    
                    <Typography variant="body2" color="text.secondary">
                        The Generated URL is for testing purposes only. Use the Service Key API to integrate it into your product.{" "}
                        <LearnMoreLink href="https://docs.unizo.ai/docs/api/platform-reference/create-service-key/" target="_blank" rel="noopener noreferrer">
                            Learn more
                        </LearnMoreLink>
                    </Typography>
                    
                    <Form {...form}>
                        <form onSubmit={handleSubmit(onSubmit)}>
                            <Stack spacing={3}>
                            <FormField
                                control={control}
                                name="customerKey"
                                render={({ field }) => (
                                    <FormItem>
                                        <TextField
                                            {...field}
                                            label="Customer Key"
                                            placeholder="Enter customer key"
                                            fullWidth
                                            error={!!errors.customerKey}
                                            helperText={errors.customerKey?.message}
                                        />
                                    </FormItem>
                                )}
                            />
                            
                            <FormField
                                control={control}
                                name="customerOrgName"
                                render={({ field }) => (
                                    <FormItem>
                                        <TextField
                                            {...field}
                                            label="Customer Name"
                                            placeholder="Enter customer name"
                                            fullWidth
                                            error={!!errors.customerOrgName}
                                            helperText={errors.customerOrgName?.message}
                                        />
                                    </FormItem>
                                )}
                            />
                            
                            {/* Category Selection */}
                            <FormField
                                control={control}
                                name="type"
                                render={({ field }) => {
                                    const provider = userFlow === "PROVIDER";
                                    const availableOptions = getOptions()?.filter(
                                        (option: any) => provider
                                            ? field.value !== option.value
                                            : !field.value?.includes(option.value)
                                    );

                                    return (
                                        <FormItem>
                                            <FormControl fullWidth>
                                                <InputLabel id="category-select-label">Select category</InputLabel>
                                                <Select
                                                    {...field}
                                                    labelId="category-select-label"
                                                    label="Select category"
                                                    multiple={!provider}
                                                    value={provider
                                                        ? (field.value || '')
                                                        : (Array.isArray(field.value) ? field.value : [])
                                                    }
                                                    onChange={e => {
                                                        field.onChange(e.target.value);
                                                        if (provider) {
                                                            getService();
                                                        }
                                                    }}
                                                    renderValue={(selected) => {
                                                        if (provider) {
                                                            const option = getOptions().find((opt: any) => opt.value === selected);
                                                            return option ? nameChecker(option.label) : "";
                                                        }
                                                        return Array.isArray(selected) && selected.length > 0 
                                                            ? selected.map(s => {
                                                                const opt = getOptions().find((o: any) => o.value === s);
                                                                return opt ? nameChecker(opt.label) : s;
                                                            }).join(', ')
                                                            : "";
                                                    }}
                                                >
                                                    {availableOptions?.map((domain: any) => (
                                                        <MenuItem key={domain?.value} value={domain?.value}>
                                                            {nameChecker(domain?.label)}
                                                        </MenuItem>
                                                    ))}
                                                </Select>
                                            </FormControl>
                                            {errors.type && (
                                                <Typography variant="caption" color="error">
                                                    {errors.type.message}
                                                </Typography>
                                            )}
                                            {/* Chips for multiple selection */}
                                            {!provider && Array.isArray(field.value) && field.value.length > 0 && (
                                                <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                                    {field.value.map((val: any) => {
                                                        const option = getOptions().find((opt: any) => opt.value === val);
                                                        return option ? (
                                                            <Chip
                                                                key={val}
                                                                label={nameChecker(option.label)}
                                                                size="small"
                                                                onDelete={() => {
                                                                    const newValues = field.value?.filter((item: any) => item !== val) || [];
                                                                    setValue("type", newValues);
                                                                }}
                                                            />
                                                        ) : null;
                                                    })}
                                                </Box>
                                            )}
                                        </FormItem>
                                    );
                                }}
                            />
                            
                            {/* Provider Selection (only shown if provider mode) */}
                            {userFlow === "PROVIDER" && (
                                <FormField
                                    control={control}
                                    name="provider"
                                    render={({ field }) => {
                                        const providerOptions = getProviderOptions();
                                        
                                        return (
                                            <FormItem>
                                                <FormControl fullWidth>
                                                    <InputLabel id="provider-select-label">Select service</InputLabel>
                                                    <Select
                                                        {...field}
                                                        labelId="provider-select-label"
                                                        label="Select service"
                                                        value={field.value || ''}
                                                        onChange={e => field.onChange(e.target.value)}
                                                        disabled={!watch('type')}
                                                        renderValue={(selected) => {
                                                            const option = providerOptions.find((opt: any) => opt.value === selected);
                                                            return option ? (
                                                                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                                    {loadImage(option?.serviceProfile, { size: 'xSmall' })}
                                                                    {option.label}
                                                                </Box>
                                                            ) : "";
                                                        }}
                                                    >
                                                        {providerOptions.length > 0 ? (
                                                            providerOptions.map((providerItem: any) => (
                                                                <MenuItem key={providerItem.value} value={providerItem.value}>
                                                                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                                        {loadImage(providerItem?.serviceProfile, { size: "xSmall" })}
                                                                        {providerItem.label}
                                                                    </Box>
                                                                </MenuItem>
                                                            ))
                                                        ) : (
                                                            <MenuItem disabled>
                                                                <Typography>No connectors enabled</Typography>
                                                            </MenuItem>
                                                        )}
                                                    </Select>
                                                </FormControl>
                                                {errors.provider && (
                                                    <Typography variant="caption" color="error">
                                                        {errors.provider.message}
                                                    </Typography>
                                                )}
                                            </FormItem>
                                        );
                                    }}
                                />
                            )}

                            <Stack direction="row" spacing={2} sx={{ mt: 3 }}>
                                <Button
                                    variant="outlined"
                                    onClick={() => setOpenDrawer(false)}
                                    fullWidth
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    variant="contained"
                                    fullWidth
                                >
                                    Generate Test Link
                                </Button>
                            </Stack>
                        </Stack>
                        </form>
                    </Form>
                </Stack>
            </Drawer>

            {/* Modal for test URL */}
            {openModal && data && (
                <ConnectUIModal
                    opened={openModal}
                    onClosed={handleCloseModal}
                    magicLink={data?.data?.formDescriptorUrl}
                />
            )}
            
            {/* Configuration Drawer */}
            <ConfigurationDrawer
                open={openConfigDrawer}
                onClose={() => {
                    setOpenConfigDrawer(false);
                    setEditConfigId(null);
                }}
                editId={editConfigId}
                onSuccess={() => {
                    refetch();
                }}
            />
        </Stack>
    );
}

