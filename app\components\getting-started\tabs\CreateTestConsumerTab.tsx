import React, { useState, useMemo, useCallback } from 'react';
import {
  Box,
  Typography,
  Stack,
  Button,
  Grid,
  useTheme,
  alpha,
} from '@mui/material';
import { ExternalLink, Check, Webhook } from 'lucide-react';
import { useNavigate } from '@remix-run/react';
import ConfigurationSection from 'components/getting-started/ConfigurationSection';
import WebhookConfigurationCard from 'components/getting-started/WebhookConfigurationCard';
import ApiWebhooksCard from 'components/getting-started/ApiWebhooksCard';
import TabNavigationFooter from 'components/getting-started/TabNavigationFooter';
import { useGetOrganization } from 'hooks/api/organization/useGetOrganization';

import CreateBiDirection from 'sections/services/setup/bi-directional/create';
import { DOMAINS } from 'data/domains';
import { extractProperties } from 'lib/utils';
import useUserDetails from 'store/user';

// Type mapping for all webhook types
const typeMap: Record<string, string> = {
  'SCM_WATCH_HOOK': 'Source Code',
  'TICKETING_WATCH_HOOK': 'Ticketing',
  'PCR_WATCH_HOOK': 'Packages & Container registry',
  'COMMS_WATCH_HOOK': 'Communications',
  'INCIDENT_WATCH_HOOK': 'Incident management',
  'VMS_WATCH_HOOK': 'Vulnerability management',
  'KEY_MGMT_WATCH_HOOK': 'Key management',
  'MONITORING_WATCH_HOOK': 'Observability',
  'IDENTITY_WATCH_HOOK': 'Identity',
  'CLOUD_INFRA_WATCH_HOOK': 'Public cloud (Infra)',
  'EDR_XDR_WATCH_HOOK': 'EDR & XDR',
  'SIEM_WATCH_HOOK': 'SIEM',
  'GEN_AI_WATCH_HOOK': 'Gen AI',
  'BLOB_STORAGE_WATCH_HOOK': 'Blob storage',
  'FILE_STORAGE_WATCH_HOOK': 'File storage',
  'PLATFORM_WATCH_HOOK': 'Platform',
};

const CUSTOM_OPTIONS = [{ label: 'Platform', value: 'PLATFORM_WATCH_HOOK', key: 'PLATFORM' }]

// Mock webhook data with PLATFORM_WATCH_HOOK
const mockWebhookData = {
  "pagination": {
    "total": 9,
    "offset": 1,
    "previous": 1,
    "next": 1
  },
  "data": [
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/7bab8867-085c-4a9b-8303-7ed59af9aec4",
      "type": "INCIDENT_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "7bab8867-085c-4a9b-8303-7ed59af9aec4",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-03-03T13:14:26.745+00:00",
        "lastUpdatedDateTime": "2025-03-15T11:00:56.011+00:00"
      },
      "data": {
        "url": "https://smee.io/gY5Tvv0DaS7OAkAH",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/77b74738-3770-4c2c-bc46-185a4d95e2b9",
      "type": "PCR_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "77b74738-3770-4c2c-bc46-185a4d95e2b9",
      "eventProfiles": [],
      "organization": {
        "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "type": "STANDARD",
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "name": "The New Snyk, Inc"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-03-19T19:41:03.347+00:00",
        "lastUpdatedDateTime": "2025-03-19T19:41:03.347+00:00"
      },
      "data": {
        "url": "https://smee.io/gY5Tvv0DaS7OAkAH",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/ecd0a902-40c5-4e35-b6ca-bd3809ffa3f3",
      "type": "IDENTITY_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "ecd0a902-40c5-4e35-b6ca-bd3809ffa3f3",
      "eventProfiles": [],
      "organization": {
        "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "type": "STANDARD",
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "name": "The New Snyk, Inc"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-04-02T13:59:14.093+00:00",
        "lastUpdatedDateTime": "2025-04-02T13:59:14.093+00:00"
      },
      "data": {
        "url": "https://mkultrahook-stripe.ultrahook.com",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/287cac77-473d-4b2c-84c0-60a0eef986c8",
      "type": "PLATFORM_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "287cac77-473d-4b2c-84c0-60a0eef986c8",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-02-16T18:59:40.323+00:00",
        "lastUpdatedDateTime": "2025-05-13T05:57:38.264+00:00"
      },
      "data": {
        "url": "https://smee.io/JMCiKcJFaerGtIMR",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/6a79ef73-de90-49a7-91d6-6ed2096da068",
      "type": "COMMS_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "6a79ef73-de90-49a7-91d6-6ed2096da068",
      "eventProfiles": [],
      "organization": {
        "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "type": "STANDARD",
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "name": "The New Snyk, Inc"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-07-09T08:18:18.134+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:27:24.567+00:00"
      },
      "data": {
        "url": "https://smee.io/oCK7nd5eO9mAcYF ",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/45813953-2ab7-4925-8317-b2acb4e6bc7f",
      "type": "VMS_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "45813953-2ab7-4925-8317-b2acb4e6bc7f",
      "eventProfiles": [],
      "organization": {
        "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "type": "STANDARD",
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "name": "The New Snyk, Inc"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-08-06T10:27:56.559+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:29:12.165+00:00"
      },
      "data": {
        "url": "https://kcdgs",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/6505cb07-bb55-4e9b-9a2e-7ffa32eace23",
      "type": "MONITORING_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "6505cb07-bb55-4e9b-9a2e-7ffa32eace23",
      "eventProfiles": [],
      "organization": {
        "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "type": "STANDARD",
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96",
        "name": "The New Snyk, Inc"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-08-06T10:33:47.335+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:33:47.335+00:00"
      },
      "data": {
        "url": "https://kcdgs",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/44534313-15e2-47a6-a3bb-fe99e7b21842",
      "type": "SCM_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "44534313-15e2-47a6-a3bb-fe99e7b21842",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T08:18:10.154+00:00",
        "lastUpdatedDateTime": "2025-08-13T11:55:29.335+00:00"
      },
      "data": {
        "url": "https://mkultrahook-stripe.ultrahook.co",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/7ab85511-23c9-421e-a89a-8853fb034f40",
      "type": "TICKETING_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "7ab85511-23c9-421e-a89a-8853fb034f40",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://mktestevent-stripe.ultrahook.com",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc1",
      "type": "KEY_MGMT_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc1",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/keymgmt",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc2",
      "type": "CLOUD_INFRA_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc2",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/cloudinfra",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc3",
      "type": "EDR_XDR_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc3",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/edrxdr",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc4",
      "type": "SIEM_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc4",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/siem",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc5",
      "type": "GEN_AI_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc5",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/genai",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc6",
      "type": "BLOB_STORAGE_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc6",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/blob",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    },
    {
      "href": "https://api.unizo.io/api/v1/organizations/e2ec25e7-e865-408b-8b1a-17602f4f4c96/configurations/abc7",
      "type": "FILE_STORAGE_WATCH_HOOK",
      "state": "SUBMITTED",
      "id": "abc7",
      "eventProfiles": [],
      "organization": {
        "id": "e2ec25e7-e865-408b-8b1a-17602f4f4c96"
      },
      "environment": {
        "id": "da13fee5-2320-4b89-a83c-6a78a7a3bd61"
      },
      "changeLog": {
        "createdDateTime": "2025-01-16T10:56:08.870+00:00",
        "lastUpdatedDateTime": "2025-08-06T10:35:47.980+00:00"
      },
      "data": {
        "url": "https://example.com/file",
        "securedSSLRequired": true,
        "contentType": "application/json"
      }
    }
  ]
};

interface CreateTestConsumerTabProps {
  onNext?: () => void;
  onPrevious?: () => void;
  isFirstTab?: boolean;
  isLastTab?: boolean;
  nextLabel?: string;
  previousLabel?: string;
}

export default function CreateTestConsumerTab({
  onNext,
  onPrevious,
  isFirstTab,
  isLastTab,
  nextLabel,
  previousLabel,
}: CreateTestConsumerTabProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const [isLoading] = useState(false);
  const [isError] = useState(false);
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [selected, setSelected] = useState<any>(null);
  const [preSelectedType, setPreSelectedType] = useState<string | null>(null)

  const { subscriptions: subscriptionsData, categories } = useUserDetails()
  const { configs } = useGetOrganization({ id: window?.authUserOrgId })

  const webHookData: any = configs?.data || []

  const enabledCategories = categories?.filter((i) => !i?.disabled)?.map((i) => i?.hook) ?? []

  // Extract platform webhook from the mock data
  const platformWebhook = useMemo(() => {
    return webHookData?.find((webhook: { type: string }) => webhook.type === 'PLATFORM_WATCH_HOOK');
  }, [webHookData]);

  // Extract API webhooks (excluding PLATFORM_WATCH_HOOK)
  const apiWebhooks = useMemo(() => {
    return webHookData?.filter((webhook: { type: string }) => webhook.type !== 'PLATFORM_WATCH_HOOK' &&  enabledCategories?.includes(webhook.type)) ?? [];
  }, [webHookData, enabledCategories]);

  const getOptions = useCallback(() => {

    const options = DOMAINS
      .map(({ hook: value, key, label }) => ({ label, value, key }));

    const filteredOptions = options.filter(option =>
      subscriptionsData.map(({ product }) => product.code?.toUpperCase()).includes(option?.key)
    );
    return filteredOptions.concat(CUSTOM_OPTIONS).filter(({ value, ...rest }: any) => {
      return (
        !extractProperties(webHookData, 'type')?.includes(value) &&
        !rest.disabled
      );
    });

  }, [webHookData, subscriptionsData]);

  // Don't pass requiredTypes - let ApiWebhooksCard show all types by default

  const onClose = () => {
    setIsOpen(false);
    setSelected(null)
  }

  const onEditWebhook = (requestedWebhook: any) => {
    setSelected({ ...requestedWebhook, name: requestedWebhook?.type });
    setIsOpen(true);
  }

  const handleConfigureWebhooks = (type: string | null = null) => {
    setIsOpen(true);
    setPreSelectedType(type)
  };

  // Check if ALL webhooks (platform + all API webhooks) are configured
  const allWebhooksConfigured = useMemo(() => {
    // Platform webhook must be configured
    const platformConfigured = !!platformWebhook && platformWebhook.state === 'SUBMITTED';

    // Get all possible webhook types (excluding PLATFORM_WATCH_HOOK)
    const allApiWebhookTypes = Object.keys(typeMap).filter(type => type !== 'PLATFORM_WATCH_HOOK' && enabledCategories?.includes(type));

    // Check if all API webhook types have a configured webhook
    const allApiWebhooksConfigured = allApiWebhookTypes.every(type =>
      apiWebhooks.some((webhook: { type: string, state: string }) => webhook.type === type && webhook.state === 'SUBMITTED')
    );

    return platformConfigured && allApiWebhooksConfigured;
  }, [platformWebhook, apiWebhooks, enabledCategories]);


  const configuredContent = (
    <Box>
      <Box
        sx={{
          backgroundColor: theme.palette.background.default,
          borderRadius: 2,
          p: 4,
          border: `1px solid ${theme.palette.divider}`,
          boxShadow: theme.palette.mode === 'dark' 
            ? `0 1px 3px ${alpha(theme.palette.common.black, 0.3)}` 
            : `0 1px 3px ${alpha(theme.palette.common.black, 0.08)}`,
        }}
      >
        <Stack spacing={4}>
          {/* Header Section */}
          <Box>
        <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 2 }}>
          <Box
            sx={{
              width: 32,
              height: 32,
              borderRadius: '50%',
              backgroundColor: theme.palette.primary.main,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: theme.palette.common.white,
            }}
          >
            <Webhook size={18} strokeWidth={3} />
          </Box>
          <Typography variant="h5" fontWeight={600}>
            Event Listeners
          </Typography>
        </Stack>
        <Typography
          variant="body1"
          sx={{
            color: theme.palette.mode === 'light'
              ? theme.palette.grey[600]
              : theme.palette.text.secondary,
            maxWidth: 800,
          }}
        >
          Setup endpoints to receive real-time notifications when data changes occur in your connected services.
        </Typography>
      </Box>

      {/* Webhook Configuration Cards */}
      <Box>
        <Stack spacing={3}>
          {/* Platform Webhook Configuration */}
          <WebhookConfigurationCard
            webhook={platformWebhook}
            isLoading={isLoading}
            isError={isError}
            isPlatformCard={true}
            onEditWebhook={() => onEditWebhook(platformWebhook)}
            onGetStarted={() => handleConfigureWebhooks('PLATFORM_WATCH_HOOK')}
          />

          {/* Per-API Webhook Configuration */}
          {!isError && (
            <ApiWebhooksCard
              configuredWebhooks={apiWebhooks}
              isLoading={isLoading}
              isError={isError}
              onConfigureWebhook={(e) => {
                handleConfigureWebhooks(e)
              }}
              onEditWebhook={onEditWebhook}
            />
          )}
        </Stack>
      </Box>
        </Stack>
      </Box>
    </Box>
  );

  return (
    <Box>
      <ConfigurationSection
        stepNumber="2"
        title="Setup Event Listeners"
        description="Get notification events when your users performs change"
        isConfigured={true}
        onGetStarted={() => handleConfigureWebhooks('PLATFORM_WATCH_HOOK')}
        configuredContent={configuredContent}
        isaddView={!platformWebhook && !apiWebhooks?.length}
      />
      <CreateBiDirection preSelectedType={preSelectedType} isEditMode={!!selected} selected={selected} isOpen={isOpen} onClose={onClose} options={getOptions()} />
      <TabNavigationFooter
        onNext={onNext}
        onPrevious={onPrevious}
        isFirstTab={isFirstTab}
        isLastTab={isLastTab}
        nextLabel={nextLabel}
        previousLabel={previousLabel}
      />
    </Box>
  );
}