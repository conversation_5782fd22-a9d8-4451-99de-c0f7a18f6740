import React, { useState } from 'react';
import {
  Box,
  Typography,
  Stack,
  Paper,
  Chip,
  Button,
  useTheme,
  alpha,
  Grid,
  IconButton,
} from '@mui/material';
import { Check, AlertCircle, ExternalLink, Copy, Webhook, Edit2 } from 'lucide-react';
import { useNavigate } from '@remix-run/react';
import { WebhookConfig } from './WebhookConfigurationCard';
import useUserDetails from 'store/user';

interface ApiWebhooksCardProps {
  configuredWebhooks: WebhookConfig[]; // Actual webhook configurations from API
  webhookTypesToDisplay?: string[]; // Which webhook types to show (defaults to all non-platform types)
  webhookTypeLabels?: Record<string, string>; // Custom labels for webhook types
  isLoading?: boolean;
  isError?: boolean;
  onConfigureWebhook?: (webhookType: string) => void; // Custom handler for configure action
  onEditWebhook?: (webhook: WebhookConfig) => void; // Custom handler for edit action
}

// Type mapping - ordered as per requirements
const typeMap: Record<string, string> = {
  'SCM_WATCH_HOOK': 'Source Code',
  'TICKETING_WATCH_HOOK': 'Ticketing',
  'PCR_WATCH_HOOK': 'Packages & Container registry',
  'COMMS_WATCH_HOOK': 'Communications',
  'INCIDENT_WATCH_HOOK': 'Incident management',
  'VMS_WATCH_HOOK': 'Vulnerability management',
  'KEY_MGMT_WATCH_HOOK': 'Key management',
  'MONITORING_WATCH_HOOK': 'Observability',
  'IDENTITY_WATCH_HOOK': 'Identity',
  'CLOUD_INFRA_WATCH_HOOK': 'Public cloud (Infra)',
  'EDR_XDR_WATCH_HOOK': 'EDR & XDR',
  'SIEM_WATCH_HOOK': 'SIEM',
  'GEN_AI_WATCH_HOOK': 'Gen AI',
  'BLOB_STORAGE_WATCH_HOOK': 'Blob storage',
  'FILE_STORAGE_WATCH_HOOK': 'File storage',
  'PLATFORM_WATCH_HOOK': 'Platform',
};

const ApiWebhooksCard: React.FC<ApiWebhooksCardProps> = ({
  configuredWebhooks,
  webhookTypesToDisplay,
  webhookTypeLabels,
  isLoading = false,
  isError = false,
  onConfigureWebhook,
  onEditWebhook
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [copiedUrl, setCopiedUrl] = useState<string | null>(null);

  const { categories } = useUserDetails()

  const enabledCategories = categories?.filter((i) => !i?.disabled)?.map((i) => i?.hook) ?? []

  // Merge default labels with custom labels
  const labels = React.useMemo(() => ({
    ...typeMap,
    ...webhookTypeLabels
  }), [webhookTypeLabels]);

  // Default webhook types to display (all non-platform types)
  const typesToDisplay = React.useMemo(() => {
    return webhookTypesToDisplay || Object.keys(typeMap).filter(type => type !== 'PLATFORM_WATCH_HOOK');
  }, [webhookTypesToDisplay]);

  const handleConfigureWebhook = (type: string) => {
    if (onConfigureWebhook) {
      onConfigureWebhook(type);
    } else {
      navigate(`/console/settings/webhooks?type=${type}`);
    }
  };

  const handleEditWebhook = (webhook: WebhookConfig) => {
    if (onEditWebhook) {
      onEditWebhook(webhook);
    } else {
      navigate(`/console/settings/webhooks?type=${webhook.type}`);
    }
  };

  const handleCopyUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    setCopiedUrl(url);
    setTimeout(() => setCopiedUrl(null), 2000);
  };

  // Create a map of webhook configurations by type
  const webhooksByType = React.useMemo(() => {
    const map: Record<string, WebhookConfig | null> = {};
    typesToDisplay.forEach(type => {
      map[type] = configuredWebhooks.find(w => w.type === type) || null;
    });
    return map;
  }, [configuredWebhooks, typesToDisplay]);

  // Get ordered webhook types based on typeMap order
  const orderedWebhookTypes = React.useMemo(() => {
    const typeOrder = Object.keys(typeMap);
    return typesToDisplay.sort((a, b) => {
      const indexA = typeOrder.indexOf(a);
      const indexB = typeOrder.indexOf(b);
      // If type is not in typeMap, put it at the end
      if (indexA === -1) return 1;
      if (indexB === -1) return -1;
      return indexA - indexB;
    });
  }, [typesToDisplay]);

  if (isError) {
    return null; // Let parent component handle error state
  }

  if (isLoading) {
    return (
      <Stack spacing={2}>
        <Typography variant="h6" fontWeight={600}>
          Category specific Event Listeners
        </Typography>
        <Stack spacing={1.5}>
          {[1, 2, 3].map((i) => (
            <Paper
              key={i}
              sx={{
                maxWidth: 800,
                p: 2,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 2,
                backgroundColor: theme.palette.background.paper,
              }}
            >
              <Box sx={{ height: 40, backgroundColor: theme.palette.action.hover, borderRadius: 1 }} />
            </Paper>
          ))}
        </Stack>
      </Stack>
    );
  }

  const WebhookCard = ({ type, webhook }: { type: string; webhook: WebhookConfig | null }) => {
    const label = labels[type] || type.replace(/_WATCH_HOOK$/, '').replace(/_/g, ' ');
    const isConfigured = webhook?.state === 'SUBMITTED';
    const isCopied = webhook?.data.url === copiedUrl;

    if (!webhook) {
      // Not configured state - show get started template
      return (
        <Paper
          sx={{
            maxWidth: 800,
            p: 2,
            border: '1px dashed rgb(255 152 0 / 0.5)',
            borderRadius: 2,
            backgroundColor: theme.palette.background.paper,
          }}
        >
          <Stack direction="row" spacing={2} alignItems="center">
            <Box
              sx={{
                width: 32,
                height: 32,
                borderRadius: 1,
                backgroundColor: alpha(theme.palette.grey[500], 0.1),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Webhook size={18} color={theme.palette.text.secondary} />
            </Box>
            <Box sx={{ flex: 1 }}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Typography
                  variant="subtitle2"
                  fontWeight={600}
                  sx={{
                    color: theme.palette.text.primary,
                  }}
                >
                  {label}
                </Typography>
                <Box
                  sx={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    px: 1,
                    py: 0.5,
                    fontSize: '0.75rem',
                    fontWeight: 500,
                    lineHeight: 1,
                    borderRadius: '9999px',
                    backgroundColor: 'rgb(255 152 0 / 0.1)',
                    color: 'rgb(255 152 0)',
                  }}
                >
                  <AlertCircle size={12} style={{ marginRight: 6 }} />
                  <span>Needs configuration</span>
                </Box>
              </Stack>
              <Typography
                variant="caption"
                sx={{
                  color: theme.palette.text.secondary,
                  fontWeight: 400
                }}
              >
                Configure webhook endpoint
              </Typography>
            </Box>
            <Button
              variant="contained"
              size="small"
              onClick={() => handleConfigureWebhook(type)}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                minWidth: 'auto',
                px: 2,
              }}
            >
              Add Endpoint
            </Button>
          </Stack>
        </Paper>
      );
    }

    // Configured state
    return (
      <Paper
        sx={{
          maxWidth: 800,
          p: 2,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 2,
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <Stack direction="row" spacing={2} alignItems="center">
          <Box
            sx={{
              width: 32,
              height: 32,
              borderRadius: 1,
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Webhook size={18} color={theme.palette.primary.main} />
          </Box>
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <Typography
                variant="subtitle2"
                fontWeight={600}
                sx={{
                  color: theme.palette.text.primary,
                }}
              >
                {label}
              </Typography>
              <Chip
                icon={<Check size={12} />}
                label="Active"
                size="small"
                sx={{
                  height: 20,
                  fontSize: '0.7rem',
                  backgroundColor: alpha(theme.palette.success.main, 0.08),
                  color: theme.palette.success.dark,
                  fontWeight: 500,
                  '& .MuiChip-icon': {
                    color: theme.palette.success.main,
                    fontSize: '12px',
                    marginLeft: '4px',
                  },
                  '& .MuiChip-label': {
                    px: 0.5,
                  },
                }}
              />
            </Stack>
            {webhook.data.url && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                <Typography
                  variant="caption"
                  sx={{
                    fontFamily: 'monospace',
                    color: theme.palette.text.secondary,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {webhook.data.url}
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => handleCopyUrl(webhook.data.url)}
                  sx={{
                    p: 0.25,
                    color: isCopied ? theme.palette.success.main : theme.palette.text.secondary,
                  }}
                >
                  {isCopied ? <Check size={12} /> : <Copy size={12} />}
                </IconButton>
              </Box>
            )}
          </Box>
          <Button
            variant="text"
            size="small"
            onClick={() => handleEditWebhook(webhook)}
            sx={{
              textTransform: 'none',
              fontWeight: 500,
              color: theme.palette.primary.main,
              textDecoration: 'underline',
              minWidth: 'auto',
              p: 0.5,
              '&:hover': {
                textDecoration: 'underline',
                backgroundColor: 'transparent',
              },
            }}
          >
            Edit
          </Button>
        </Stack>
      </Paper>
    );
  };

  return (
    <Stack spacing={2}>
      <Typography variant="h6" fontWeight={600}>
        Category specific Event Listeners
      </Typography>
      <Typography
        variant="body2"
        sx={{
          color: theme.palette.mode === 'light'
            ? theme.palette.grey[600]
            : theme.palette.text.secondary
        }}
      >
        Configure event listener endpoints for each category for greater control and scalability.
      </Typography>

      <Stack spacing={1.5}>
        {orderedWebhookTypes
          ?.filter((i) => enabledCategories?.includes(i))
          ?.map((type) => (
            <WebhookCard key={type} type={type} webhook={webhooksByType[type]} />
          ))}
      </Stack>
    </Stack>
  );
};

export default ApiWebhooksCard;