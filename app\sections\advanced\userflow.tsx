import { useState, useEffect } from "react";
import {
  Box,
  TextField,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
  Grid,
  useTheme,
  FormControl,
  Stack,
  Checkbox,
  Divider,
  alpha,
} from "@mui/material";
import MainCard from "components/MainCard";
import { useNavigate, useParams } from "@remix-run/react";

import { useForm } from "react-hook-form";
import { Form, FormField, FormItem } from "components/@extended/Form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  getHighlightedStyles,
  behaviorStyles,
} from "../../sections/security/key-protection/helper";
import { useGetDockProfile } from "hooks/api/dockProfiles/useDockProfile";
import LearnMoreLink from "components/@extended/LearnMoreLink";

type WatchFormValues = z.infer<typeof watchFormScheme>;

const watchFormScheme = z.object({
  pageLayout: z.enum(["POP_UP", "EMBEDDED"], {
    required_error: "Please select a page layout.",
  }),

  name: z.string().nonempty("Name is required")
    .regex(/^[a-zA-Z0-9_-]+$/, "Name can only contain letters, numbers, hyphens, or underscores")
    .min(3, "Name must be at least 3 characters long")
    .max(30, "Name cannot be longer than 30 characters"),
 frontEndUrl: z
    .union([z.string().url("URl is in invalid URL format."), z.string().length(0)]) // allow empty
    .transform((val) => (val === "" ? undefined : val)) // treat empty as undefined
    .refine((val) => !val || val.startsWith("https://"), {
      message: "Custom domain must start with https://",
    }),
});


const defaultValues: Partial<WatchFormValues> = {
  pageLayout: "EMBEDDED",
  name: "",
  frontEndUrl: "",
};

export default function CreateUserFlow() {
  const { mode, id } = useParams(); // Extract mode and id from URL params
  const navigate = useNavigate();
  const { attemptCreateDockProfile, attemptEditDockProfile, getDockProfileById } = useGetDockProfile();
  const { palette }: any = useTheme();
  
  // Determine if we're in edit mode
  const isEditMode = mode === 'edit' && !!id;
  const [isLoading, setIsLoading] = useState(false);
  const [useCustomUrl, setUseCustomUrl] = useState(false);
  const [enableScopedAccess, setEnableScopedAccess] = useState(false);

  const form = useForm<WatchFormValues>({
    resolver: zodResolver(watchFormScheme),
    defaultValues,
    mode: "onChange",
  });

  const selectedLayout = form.watch("pageLayout");

  // Fetch existing data for edit mode using React Query
  const { data: existingData, isLoading: fetchingData, error } = getDockProfileById(id || '');

  // Pre-fill form with existing data when data is loaded
  useEffect(() => {
    if (isEditMode && existingData && !fetchingData) {
      const hasCustomUrl = existingData.frontendUrl && existingData.frontendUrl !== "https://dock.unizo.ai";
      setUseCustomUrl(hasCustomUrl);
      form.reset({
        name: existingData.name || "",
        frontEndUrl: hasCustomUrl ? existingData.frontendUrl :  "",
        pageLayout: existingData.pageLayout || "EMBEDDED",
      });
    }
  }, [isEditMode, existingData, fetchingData, form]);

  // Handle error in data fetching
  useEffect(() => {
    if (error && isEditMode) {
      console.error("Error fetching existing data:", error);
      // Optionally show toast error and redirect
      // toast.error("Failed to load configuration data");
      // navigate("/console/connect-UI");
    }
  }, [error, isEditMode, navigate]);

  const handleSubmit = async (values: WatchFormValues) => {
    setIsLoading(true);
    
    try {
      if (isEditMode) {
        // Update existing configuration
        const patchPayload = Object.entries(form.formState.dirtyFields).reduce((acc: any, [key]) => {
          let value = values[key as keyof WatchFormValues];
          // Handle frontEndUrl - send default if checkbox is unchecked
          if (key === 'frontEndUrl') {
            value = useCustomUrl && values.frontEndUrl ? values.frontEndUrl : "https://dock.unizo.ai";
          }
          acc.push({
            op: "replace",
            path: `/${key === 'frontEndUrl' ? 'frontendUrl' : key}`, // Handle field name mapping
            value: value
          });
          return acc;
        }, []);

        // If checkbox state changed, we need to update frontendUrl
        if (useCustomUrl !== (existingData?.frontendUrl && existingData.frontendUrl !== "https://dock.unizo.ai")) {
          const urlExists = patchPayload.some((p: any) => p.path === '/frontendUrl');
          if (!urlExists) {
            patchPayload.push({
              op: "replace",
              path: "/frontendUrl",
              value: useCustomUrl && values.frontEndUrl ? values.frontEndUrl : "https://dock.unizo.ai"
            });
          }
        }

        await attemptEditDockProfile(id, patchPayload);
        navigate("/console/connect-UI");
      } else {
        // Create new configuration
        const payload = {
          type: "CUSTOM",
          pageLayout: values.pageLayout,
          name: values.name,
          userFlow: {
            type: "CATEGORY",
          },
          frontendUrl: useCustomUrl && values.frontEndUrl ? values.frontEndUrl : "https://dock.unizo.ai",
        };

        await attemptCreateDockProfile(payload);
        navigate("/console/connect-UI");
      }
    } catch (error) {
      console.error(isEditMode ? "Error updating configuration:" : "Error creating configuration:", error);
      setIsLoading(false);
    }
  };

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h3" fontWeight={700} gutterBottom>
          {isEditMode ? "Edit Connect UI" : "New Connect UI"}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {isEditMode 
            ? "Update your authentication experience configuration."
            : "Set up pre-built authentication experiences for your end users."
          }
        </Typography>
      </Grid>
      
      {fetchingData ? (
        <Grid item xs={12}>
          <MainCard>
            <Typography>Loading configuration...</Typography>
          </MainCard>
        </Grid>
      ) : (
        <Grid item xs={12}>
          <MainCard sx={{ maxWidth: 800 }}>
            <Form {...form}>
              <Stack spacing={4}>
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <Stack spacing={1}>
                      <Typography variant="subtitle1" fontWeight={600}>
                        Name
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        The unique string used to identify this Connect UI.
                      </Typography>
                      <FormControl>
                        <TextField
                          fullWidth
                          size="small"
                          placeholder="Enter a unique name"
                          {...field}
                          sx={{ maxWidth: 500 }}
                        />
                      </FormControl>
                    </Stack>
                  </FormItem>
                )}
              />
              
              <Stack spacing={2}>
                <Stack spacing={1}>
                  <Typography variant="subtitle1" fontWeight={600}>
                    Change Connect UI's URL using Custom Domains
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    By default, Connect UI loads from https://dock.unizo.ai in your product's web UI (rendered in your user's browser).
                    You can optionally use a custom domain so the URL matches your application. This requires a small network/DNS configuration on your web UI's domain.
                    &nbsp;<LearnMoreLink
                      href="https://docs.unizo.ai/docs/unizo-console/connect-ui/#how-to-create-a-dock-profile"
                      underline="hover"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Learn more
                    </LearnMoreLink>
                  </Typography>
                </Stack>
                <FormControl>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={useCustomUrl}
                        onChange={(e) => {
                          setUseCustomUrl(e.target.checked);
                          if (!e.target.checked) {
                            form.setValue('frontEndUrl', undefined);
                          }
                        }}
                        size="small"
                      />
                    }
                    label={
                      <Typography variant="body2">
                        Enable custom domain
                      </Typography>
                    }
                  />
                  {useCustomUrl && (
                    <FormField
                      control={form.control}
                      name="frontEndUrl"
                      render={({ field, fieldState }) => (
                        <TextField
                          size="small"
                          fullWidth
                          placeholder="https://dock.acme.com"
                          {...field}
                          value={field.value || ""}
                          error={!!fieldState.error}
                          helperText={fieldState.error?.message}
                          sx={{ mt: 1, maxWidth: 500 }}
                        />
                      )}
                    />
                  )}
                </FormControl>
              </Stack>
              {/* Enable Scoped Access - To be implemented later */}
              <Stack spacing={2} sx={{ mt: 3 }}>
                <Stack spacing={1}>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Typography variant="subtitle1" fontWeight={600}>
                      Enable Scoped Access
                    </Typography>
                  </Stack>
                  <Typography variant="body2" color="text.secondary">
                    Control access to specific features and data within Connect UI.
                  </Typography>
                </Stack>
                <FormControl>
                  <FormControlLabel
                    control={
                      <Checkbox
                        size="small"
                        checked={enableScopedAccess}
                        onChange={(e) => setEnableScopedAccess(e.target.checked)}
                      />
                    }
                    label={
                      <Typography variant="body2">
                        Enable scoped access
                      </Typography>
                    }
                  />
                </FormControl>
              </Stack>

              <FormField
                control={form.control}
                name="pageLayout"
                render={({ field }) => (
                  <Stack spacing={2} sx={{ mt: 3 }}>
                    <Stack spacing={1}>
                      <Typography variant="subtitle1" fontWeight={600}>
                        Select a layout
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Choose how Connect UI will appear in your application.
                      </Typography>
                    </Stack>
                    <FormControl>
                      <RadioGroup {...field}>
                        <Grid container spacing={2}>
                          {["embedded", "popup"].map((type) => {
                            const value = type === "embedded" ? "EMBEDDED" : "POP_UP";
                            const isSelected = selectedLayout === value;
                            const isPopup = value === "POP_UP";

                            return (
                              <Grid item xs={12} sm={6} key={type}>
                                <Box
                                  sx={{
                                    border: `1px solid ${isSelected ? palette.primary.main : palette.divider}`,
                                    borderRadius: 1,
                                    p: 2,
                                    height: '100%',
                                    cursor: isPopup ? "not-allowed" : "pointer",
                                    opacity: isPopup ? 0.6 : 1,
                                    backgroundColor: isSelected ? alpha(palette.primary.main, 0.05) : 'transparent',
                                    transition: 'all 0.2s ease',
                                    '&:hover': {
                                      borderColor: isPopup ? palette.divider : palette.primary.main,
                                      backgroundColor: isPopup ? 'transparent' : alpha(palette.primary.main, 0.05)
                                    }
                                  }}
                                  onClick={() => !isPopup && field.onChange(value)}
                                >
                                  <FormControlLabel
                                    value={value}
                                    control={
                                      <Radio 
                                        checked={isSelected} 
                                        size="small"
                                        disabled={isPopup}
                                      />
                                    }
                                    label={
                                      <Stack spacing={0.5} ml={1}>
                                        <Typography variant="body2" fontWeight={isSelected ? 600 : 400}>
                                          {type === "popup" ? "Pop Up" : "Embedded"}
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary">
                                          {type === "embedded"
                                            ? "Integrates directly within your application page"
                                            : "Opens in a modal window (Coming soon)"}
                                        </Typography>
                                      </Stack>
                                    }
                                    sx={{ m: 0, width: '100%' }}
                                  />
                                </Box>
                              </Grid>
                            );
                          })}
                        </Grid>
                      </RadioGroup>
                    </FormControl>
                  </Stack>
                )}
              />
            </Stack>
          </Form>
          
          {/* Action Buttons */}
          <Box display="flex" justifyContent="flex-end" gap={2} mt={4}>
            <Button
              variant="outlined"
              onClick={() => navigate("/console/connect-UI")}
              sx={{ minWidth: 100 }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={form.handleSubmit(handleSubmit)}
              disabled={isLoading}
              sx={{ minWidth: 100 }}
            >
              {isLoading 
                ? (isEditMode ? "Updating..." : "Creating...") 
                : (isEditMode ? "Update" : "Create")
              }
            </Button>
          </Box>
        </MainCard>
        </Grid>
      )}
    </Grid>
  );
}