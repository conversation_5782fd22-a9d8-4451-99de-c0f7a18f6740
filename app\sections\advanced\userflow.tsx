import { useState, useEffect } from "react";
import {
  Box,
  TextField,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
  Grid,
  useTheme,
  FormControl,
  Stack,
  Checkbox,
} from "@mui/material";
import MainCard from "components/MainCard";
import { useNavigate, useParams } from "@remix-run/react";

import { useForm } from "react-hook-form";
import { Form, FormField, FormItem } from "components/@extended/Form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  getHighlightedStyles,
  behaviorStyles,
} from "../../sections/security/key-protection/helper";
import { useGetDockProfile } from "hooks/api/dockProfiles/useDockProfile";
import LearnMoreLink from "components/@extended/LearnMoreLink";

type WatchFormValues = z.infer<typeof watchFormScheme>;

const watchFormScheme = z.object({
  pageLayout: z.enum(["POP_UP", "EMBEDDED"], {
    required_error: "Please select a page layout.",
  }),

  name: z.string().nonempty("Name is required")
    .regex(/^[a-zA-Z0-9_-]+$/, "Name can only contain letters, numbers, hyphens, or underscores")
    .min(3, "Name must be at least 3 characters long")
    .max(30, "Name cannot be longer than 30 characters"),
 frontEndUrl: z
    .union([z.string().url("URl is in invalid URL format."), z.string().length(0)]) // allow empty
    .transform((val) => (val === "" ? undefined : val)) // treat empty as undefined
    .refine((val) => !val || val.startsWith("https://"), {
      message: "Custom domain must start with https://",
    }),
});


const defaultValues: Partial<WatchFormValues> = {
  pageLayout: "EMBEDDED",
  name: "",
  frontEndUrl: "",
};

export default function CreateUserFlow() {
  const { mode, id } = useParams(); // Extract mode and id from URL params
  const navigate = useNavigate();
  const { attemptCreateDockProfile, attemptEditDockProfile, getDockProfileById } = useGetDockProfile();
  const { palette }: any = useTheme();
  
  // Determine if we're in edit mode
  const isEditMode = mode === 'edit' && !!id;
  const [isLoading, setIsLoading] = useState(false);
  const [useCustomUrl, setUseCustomUrl] = useState(false);

  const form = useForm<WatchFormValues>({
    resolver: zodResolver(watchFormScheme),
    defaultValues,
    mode: "onChange",
  });

  const selectedLayout = form.watch("pageLayout");

  // Fetch existing data for edit mode using React Query
  const { data: existingData, isLoading: fetchingData, error } = getDockProfileById(id || '');

  // Pre-fill form with existing data when data is loaded
  useEffect(() => {
    if (isEditMode && existingData && !fetchingData) {
      const hasCustomUrl = existingData.frontendUrl && existingData.frontendUrl !== "https://dock.unizo.ai";
      setUseCustomUrl(hasCustomUrl);
      form.reset({
        name: existingData.name || "",
        frontEndUrl: hasCustomUrl ? existingData.frontendUrl :  "",
        pageLayout: existingData.pageLayout || "EMBEDDED",
      });
    }
  }, [isEditMode, existingData, fetchingData, form]);

  // Handle error in data fetching
  useEffect(() => {
    if (error && isEditMode) {
      console.error("Error fetching existing data:", error);
      // Optionally show toast error and redirect
      // toast.error("Failed to load configuration data");
      // navigate("/console/connect-UI");
    }
  }, [error, isEditMode, navigate]);

  const handleSubmit = async (values: WatchFormValues) => {
    setIsLoading(true);
    
    try {
      if (isEditMode) {
        // Update existing configuration
        const patchPayload = Object.entries(form.formState.dirtyFields).reduce((acc: any, [key]) => {
          let value = values[key as keyof WatchFormValues];
          // Handle frontEndUrl - send default if checkbox is unchecked
          if (key === 'frontEndUrl') {
            value = useCustomUrl && values.frontEndUrl ? values.frontEndUrl : "https://dock.unizo.ai";
          }
          acc.push({
            op: "replace",
            path: `/${key === 'frontEndUrl' ? 'frontendUrl' : key}`, // Handle field name mapping
            value: value
          });
          return acc;
        }, []);

        // If checkbox state changed, we need to update frontendUrl
        if (useCustomUrl !== (existingData?.frontendUrl && existingData.frontendUrl !== "https://dock.unizo.ai")) {
          const urlExists = patchPayload.some((p: any) => p.path === '/frontendUrl');
          if (!urlExists) {
            patchPayload.push({
              op: "replace",
              path: "/frontendUrl",
              value: useCustomUrl && values.frontEndUrl ? values.frontEndUrl : "https://dock.unizo.ai"
            });
          }
        }

        await attemptEditDockProfile(id, patchPayload);
        navigate("/console/connect-UI");
      } else {
        // Create new configuration
        const payload = {
          type: "CUSTOM",
          pageLayout: values.pageLayout,
          name: values.name,
          userFlow: {
            type: "CATEGORY",
          },
          frontendUrl: useCustomUrl && values.frontEndUrl ? values.frontEndUrl : "https://dock.unizo.ai",
        };

        await attemptCreateDockProfile(payload);
        navigate("/console/connect-UI");
      }
    } catch (error) {
      console.error(isEditMode ? "Error updating configuration:" : "Error creating configuration:", error);
      setIsLoading(false);
    }
  };

  return (
    <Grid>
      <Typography variant="h4" fontWeight="bold" gutterBottom>
        {isEditMode ? "Edit Connect UI Configuration" : "Configure Connect UI"}
      </Typography>
      <Typography mb={3}>
        {isEditMode 
          ? "Update your authentication experience configuration."
          : "Set up pre-built authentication experiences for your end users."
        }
      </Typography>
      
      {fetchingData ? (
        <MainCard>
          <Typography>Loading configuration...</Typography>
        </MainCard>
      ) : (
        <MainCard>
          <Form {...form}>
            <Stack spacing={3}>
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem
                    label="Name"
                    description="The unique string used to identify this Connect UI."
                  >
                    <FormControl>
                      <TextField
                        style={{ maxWidth: 400 }}
                        placeholder="Please enter name"
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormItem
                label="Custom Domain"
                                   description={
                      (
                        <>
                        Use this feature with your DNS provider to have Connect UI's network calls originate from your domain instead of the Unizo's native base URL.{" "}
                          <LearnMoreLink
                            href="https://docs.unizo.ai/docs/unizo-console/connect-ui/#how-to-create-a-dock-profile"
                            underline="hover"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            Learn more
                          </LearnMoreLink>
                        </>
                      ) as any
                    }
              >
                <FormControl>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={useCustomUrl}
                        onChange={(e) => {
                          setUseCustomUrl(e.target.checked);
                          if (!e.target.checked) {
                            form.setValue('frontEndUrl', undefined);
                          }
                        }}
                      />
                    }
                    label="Use Custom Domain"
                  />
                  {useCustomUrl && (
               <FormField
                  control={form.control}
                  name="frontEndUrl"
                 render={({ field, fieldState }) => (
                  <TextField
                 style={{ maxWidth: 400, marginTop: 8 }}
                 placeholder="Example: https://dock.acme.com"
                  fullWidth
                  {...field}
                  value={field.value || ""}
                  error={!!fieldState.error}
                  helperText={fieldState.error?.message}
                  />
                  )}
                  />

                  )}
                </FormControl>
              </FormItem>
            {/* <FormField
              control={form.control}
              name="userFlow"
              render={({ field }) => (
                <FormItem label="Select a user flow" description="Pre-built user flows enable integration into your product journey, allowing you
                to select a provider or category and configure the integration experience.">
                  <FormControl fullWidth>
                    <RadioGroup {...field}>
                      <Grid container spacing={2}>
                        {["category", "provider"].map((type) => {
                          const value = type === "category" ? "CATEGORY" : "PROVIDER";
                          const isSelected = selectedUserFlow === value;
                          const isPopup = value === "PROVIDER";

                          return (
                            <Grid item xs={12} md={6} lg={4} xl={4} key={type}>
                              <MainCard
                                onClick={() => field.onChange(value)}
                                sx={{
                                  borderRadius: "5px",
                                  borderWidth: 2,
                                  transition: "all 0.3s ease",
          //                         cursor: isPopup ? "not-allowed" : isSelected ? "pointer" : "not-allowed",
          // opacity: isPopup ? 0.6 : isSelected ? 1 : 0.5,
          // pointerEvents: isPopup ? "none" : "auto", // Disable interactions for popup
                                  ...(isSelected ? behaviorStyles(palette) : {}),
                                  ...(isSelected ? getHighlightedStyles(palette) : {}),
                                }}
                              >
                                <Stack display="flex" flexDirection="row" alignItems="start" gap="0.6rem">
                                  <FormControlLabel
                                    value={value}
                                    control={<Radio checked={isSelected} />}
                                    label=""
                                  />
                                  <Stack display="flex" flexDirection="column" gap="0.6rem">
                                    <Typography variant="h5">
                                      {type === "category" ? "Category" : "Service"}
                                    </Typography>
                                    <Typography variant="body1" color="textSecondary">
                                      {type === "category"
                                        ? "Creates a dock URL for the selected categories."
                                        : "Creates a dock URL for the selected service."}
                                    </Typography>
                                  </Stack>
                                </Stack>
                              </MainCard>
                            </Grid>
                          );
                        })}
                      </Grid>
                    </RadioGroup>
                  </FormControl>
                </FormItem>
              )}
            /> */}

              <FormField
                control={form.control}
                name="pageLayout"
                render={({ field }) => (
                  <FormItem label="Select a layout">
                    <FormControl fullWidth>
                      <RadioGroup {...field}>
                        <Grid container spacing={2}>
                          {["embedded", "popup"].map((type) => {
                            const value =
                              type === "embedded" ? "EMBEDDED" : "POP_UP";
                            const isSelected = selectedLayout === value;
                            const isPopup = value === "POP_UP";

                            return (
                              <Grid item xs={12} md={6} lg={4} xl={4} key={type}>
                                <MainCard
                                  onClick={() => field.onChange(value)}
                                  sx={{
                                    borderRadius: "5px",
                                    borderWidth: 2,
                                    transition: "all 0.3s ease",
                                    cursor: isPopup
                                      ? "not-allowed"
                                      : isSelected
                                        ? "pointer"
                                        : "not-allowed",
                                    opacity: isPopup ? 0.6 : isSelected ? 1 : 0.5,
                                    pointerEvents: isPopup ? "none" : "auto",
                                    ...(isSelected
                                      ? behaviorStyles(palette)
                                      : {}),
                                    ...(isSelected
                                      ? getHighlightedStyles(palette)
                                      : {}),
                                  }}
                                >
                                  <Stack
                                    display="flex"
                                    flexDirection="row"
                                    alignItems="start"
                                    gap="0.6rem"
                                  >
                                    <FormControlLabel
                                      value={value}
                                      control={<Radio checked={isSelected} />}
                                      label=""
                                    />
                                    <Stack
                                      display="flex"
                                      flexDirection="column"
                                      gap="0.6rem"
                                    >
                                      <Typography variant="h5">
                                        {type === "popup" ? "Pop Up" : "Embedded"}
                                      </Typography>
                                      <Typography
                                        variant="body1"
                                        color="textSecondary"
                                      >
                                        {type === "embedded"
                                          ? "Renders the integration flow directly within your application page, allowing users to authenticate and connect services."
                                          : "Displays a pop-up for users to authenticate and connect their accounts without leaving the page context."}
                                      </Typography>
                                    </Stack>
                                  </Stack>
                                </MainCard>
                              </Grid>
                            );
                          })}
                        </Grid>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
            </Stack>
          </Form>
          
          {/* Action Buttons */}
          <Box display="flex" justifyContent="space-between" mt={3}>
            <Button
              variant="outlined"
              onClick={() => navigate("/console/connect-UI")}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={form.handleSubmit(handleSubmit)}
              disabled={isLoading}
            >
              {isLoading 
                ? (isEditMode ? "Updating..." : "Creating...") 
                : (isEditMode ? "Update" : "Submit")
              }
            </Button>
          </Box>
        </MainCard>
      )}
    </Grid>
  );
}