import React from 'react';
import {
  Box,
  Typography,
  Stack,
  Paper,
  Chip,
  Button,
  useTheme,
  alpha,
} from '@mui/material';
import { Check, AlertCircle, ExternalLink } from 'lucide-react';
import { useNavigate } from '@remix-run/react';

export interface WebhookData {
  url: string;
  securedSSLRequired: boolean;
  contentType: string;
}

export interface WebhookConfig {
  href: string;
  type: string;
  state: string;
  id: string;
  data: WebhookData;
  changeLog: {
    createdDateTime: string;
    lastUpdatedDateTime: string;
  };
  eventProfiles?: any[];
  organization?: {
    id: string;
    name?: string;
    type?: string;
    href?: string;
  };
  environment?: {
    id: string;
  };
}

interface WebhookConfigurationCardProps {
  webhook?: WebhookConfig;
  isLoading?: boolean;
  isError?: boolean;
  onRetry?: () => void;
  isPlatformCard?: boolean; // Determines if this is for platform webhook
  onEditWebhook: () => void
  onGetStarted: () => void
}

const WebhookConfigurationCard: React.FC<WebhookConfigurationCardProps> = ({
  webhook,
  isLoading = false,
  isError = false,
  onRetry,
  isPlatformCard = false,
  onEditWebhook,
  onGetStarted
}) => {
  const theme = useTheme();
  const navigate = useNavigate();

  const isConfigured = webhook?.state === 'SUBMITTED';

  const handleConfigureWebhook = () => {
    onEditWebhook?.()
  };

  // Error state
  if (isError) {
    return (
      <Paper
        sx={{
          p: 3,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 2,
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <Stack spacing={2} alignItems="center" textAlign="center">
          <AlertCircle size={32} color={theme.palette.error.main} />
          <Typography variant="h6" fontWeight={600}>
            Something went wrong
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.mode === 'light'
                ? theme.palette.grey[600]
                : theme.palette.text.secondary
            }}
          >
            Unable to load webhook configuration. Please try again later.
          </Typography>
          <Button
            variant="outlined"
            onClick={onRetry || (() => window.location.reload())}
            size="small"
            sx={{
              textTransform: 'none',
              fontWeight: 500,
            }}
          >
            Retry
          </Button>
        </Stack>
      </Paper>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <Paper
        sx={{
          maxWidth: 800,
          p: 3,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 2,
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <Stack spacing={2}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="h6" fontWeight={600}>
              Platform Event Listener
            </Typography>
            <Box sx={{ width: 80, height: 20, backgroundColor: theme.palette.action.hover, borderRadius: 1 }} />
          </Stack>
          <Box sx={{ height: 20, backgroundColor: theme.palette.action.hover, borderRadius: 1 }} />
          <Box sx={{ height: 40, backgroundColor: theme.palette.action.hover, borderRadius: 1 }} />
        </Stack>
      </Paper>
    );
  }

  // Not found state - only show for platform card when webhook is missing
  if (isPlatformCard && !webhook) {
    return (
      <Paper
        sx={{
          maxWidth: 800,
          p: 3,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 2,
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <Stack spacing={2.5}>
          <Stack direction="row" spacing={2} alignItems="flex-start">
            <Box
              sx={{
                width: 32,
                height: 32,
                borderRadius: '50%',
                backgroundColor: alpha('#f59e0b', 0.1),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
              }}
            >
              <AlertCircle size={18} color="#f59e0b" />
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Platform Event Listener Not Configured
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: theme.palette.mode === 'light'
                    ? theme.palette.grey[600]
                    : theme.palette.text.secondary,
                  mb: 2,
                }}
              >
                Set up a platform webhook to receive notifications for all platform-level events including user authentication, connection status changes, and system updates.
              </Typography>

              <Box
                sx={{
                  backgroundColor: alpha('#f59e0b', 0.08),
                  borderRadius: 1,
                  p: 1.5,
                  mb: 2,
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: '#f59e0b',
                    fontWeight: 400,
                    fontSize: '0.875rem',
                  }}
                >
                  This step is important to use Connect UI, a simple no-code solution to allow your users to securely authenticate connectors
                </Typography>
              </Box>

              <Button
                variant="contained"
                size="small"
                onClick={() => onGetStarted()}
                endIcon={<ExternalLink size={14} />}
                sx={{
                  textTransform: 'none',
                  fontWeight: 500,
                }}
              >
                Add Endpoint
              </Button>
            </Box>
          </Stack>
        </Stack>
      </Paper>
    );
  }

  // Configured state
  return (
    <Paper
      sx={{
        maxWidth: 800,
        p: 3,
        border: `1px solid ${theme.palette.divider}`,
        borderRadius: 2,
        backgroundColor: theme.palette.background.paper,
      }}
    >
      <Stack spacing={2}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" fontWeight={600}>
            Platform Event Listener
          </Typography>
          {isConfigured ? (
            <Button
              variant="text"
              size="small"
              onClick={handleConfigureWebhook}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                color: theme.palette.primary.main,
                textDecoration: 'underline',
                '&:hover': {
                  textDecoration: 'underline',
                  backgroundColor: 'transparent',
                },
              }}
            >
              Edit
            </Button>
          ) : (
            <Chip
              icon={<AlertCircle size={14} />}
              label="Not Configured"
              size="small"
              color="warning"
              sx={{
                fontWeight: 500,
                '& .MuiChip-icon': {
                  fontSize: '14px',
                },
              }}
            />
          )}
        </Stack>

        <Typography
          variant="body2"
          sx={{
            color: theme.palette.mode === 'light'
              ? theme.palette.grey[600]
              : theme.palette.text.secondary
          }}
        >
          Receive notifications for all platform-level events including integration creation, status changes, and system updates.
        </Typography>

        {isConfigured && webhook.data.url && (
          <Box
            sx={{
              backgroundColor: theme.palette.mode === 'light'
                ? theme.palette.grey[50]
                : alpha(theme.palette.grey[900], 0.5),
              borderRadius: 1,
              p: 2,
              fontFamily: 'monospace',
              fontSize: '0.875rem',
              color: theme.palette.text.secondary,
              wordBreak: 'break-all',
            }}
          >
            {webhook.data.url}
          </Box>
        )}
      </Stack>
    </Paper>
  );
};

export default WebhookConfigurationCard;