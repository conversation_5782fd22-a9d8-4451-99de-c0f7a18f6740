import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Stack,
  Button,
  Avatar,
  AvatarGroup,
  Chip,
  useTheme,
  alpha,
} from '@mui/material';
import { ChevronRight, Check, Shield, Lock } from 'lucide-react';
import { useGettingStarted } from '../context/GettingStartedContext';
import { toast } from 'sonner';
import { getQuickStartDomains } from 'data/domains';

interface CategorySelectionProps {
  onNext: () => void;
  onBack: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

// Get categories from centralized domain data
const CATEGORIES = getQuickStartDomains().map(domain => ({
  code: domain.value,
  name: domain.label,
  description: domain.description,
  icon: domain.icon,
  color: domain.color,
  connectors: domain.connectors,
}));

export default function CategorySelection({ onNext, onBack, isFirstStep }: CategorySelectionProps) {
  const theme = useTheme();
  const { 
    selectedCategories, 
    setSelectedCategories, 
    predefinedCategories, 
    isPredefinedTenant 
  } = useGettingStarted();

  const toggleCategory = (categoryCode: string) => {
    setSelectedCategories(
      selectedCategories.includes(categoryCode)
        ? selectedCategories.filter(c => c !== categoryCode)
        : [...selectedCategories, categoryCode]
    );
  };

  const handleNext = () => {
    if (selectedCategories.length === 0) {
      toast.error('Please select at least one API category');
      return;
    }
    onNext();
  };

  const renderCategoryCard = (category: typeof CATEGORIES[0]) => {
    const isSelected = selectedCategories.includes(category.code);
    const isPredefinedCategory = isPredefinedTenant && predefinedCategories?.includes(category.code);
    const isDisabled = isPredefinedTenant && !isPredefinedCategory;
    const Icon = category.icon;

    return (
      <Grid item xs={12} sm={6} md={4} key={category.code}>
        <Card
          onClick={() => {
            // Only allow clicking in trial mode or for predefined categories
            if (!isPredefinedTenant) {
              toggleCategory(category.code);
            }
          }}
          sx={{
            cursor: isDisabled || (isPredefinedTenant && isSelected) ? 'not-allowed' : 'pointer',
            height: '100%',
            border: 1,
            borderColor: isSelected ? theme.palette.primary.main : theme.palette.divider,
            backgroundColor: theme.palette.background.paper,
            transition: 'all 0.2s ease',
            position: 'relative',
            opacity: isDisabled ? 0.5 : 1,
            '&:hover': !isDisabled && !isPredefinedTenant ? {
              borderColor: theme.palette.primary.main,
              boxShadow: theme.shadows[2],
            } : {},
          }}
        >
          {/* Show checkmark for selected categories or lock for disabled categories in predefined mode */}
          {(isSelected || (isPredefinedTenant && !isPredefinedCategory)) && (
            <Box
              sx={{
                position: 'absolute',
                top: 12,
                right: 12,
                width: 20,
                height: 20,
                borderRadius: '50%',
                backgroundColor: isSelected ? theme.palette.primary.main : theme.palette.grey[400],
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {isSelected ? (
                <Check size={14} color={theme.palette.primary.contrastText} />
              ) : (
                <Lock size={12} color={theme.palette.common.white} />
              )}
            </Box>
          )}
          <CardContent sx={{ height: '100%', p: 3 }}>
            <Stack spacing={2} height="100%">
              {/* Icon and Title */}
              <Stack direction="row" alignItems="flex-start" spacing={2}>
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: 1,
                    backgroundColor: isSelected 
                      ? alpha(theme.palette.primary.main, 0.1)
                      : alpha(theme.palette.text.primary, 0.04),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexShrink: 0,
                  }}
                >
                  <Icon 
                    size={20} 
                    color={isSelected ? theme.palette.primary.main : theme.palette.text.secondary}
                  />
                </Box>
                <Box flex={1}>
                  <Typography variant="h4" fontWeight={600} gutterBottom>
                    {category.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {category.description}
                  </Typography>
                </Box>
              </Stack>

              {/* Connector Avatars */}
              <Box sx={{ mt: 'auto' }}>
                {category.connectors && category.connectors.items.length > 0 && (
                  <Stack direction="row" alignItems="center" spacing={0.75}>
                    <AvatarGroup 
                      max={4} 
                      sx={{ 
                        '& .MuiAvatar-root': { 
                          width: 40, 
                          height: 40,
                          fontSize: '1rem',
                          backgroundColor: theme.palette.background.paper,
                          border: `2px solid ${theme.palette.background.paper}`,
                          boxShadow: `0 0 0 1px ${theme.palette.divider}`,
                        },
                        '& .MuiAvatarGroup-avatar': {
                          width: 40,
                          height: 40,
                          fontSize: '1rem',
                          backgroundColor: theme.palette.grey[100],
                          color: theme.palette.text.primary,
                          fontWeight: 600,
                          border: `2px solid ${theme.palette.background.paper}`,
                          boxShadow: `0 0 0 1px ${theme.palette.divider}`,
                        }
                      }}
                    >
                      {category.connectors.items.map((connector) => (
                        <Avatar
                          key={connector.id}
                          src={connector.image}
                          alt="Connector"
                          sx={{
                            '& img': {
                              objectFit: 'contain',
                              padding: '8px',
                              width: '76%',
                              height: '100%',
                            }
                          }}
                        />
                      ))}
                    </AvatarGroup>
                    {category.connectors.total > 4 && (
                      <Chip
                        label={`+${category.connectors.total - 4}`}
                        size="small"
                        sx={{
                          height: 28,
                          fontSize: '0.813rem',
                          fontWeight: 600,
                          backgroundColor: alpha(theme.palette.grey[300], 0.5),
                          color: theme.palette.text.primary,
                          '& .MuiChip-label': {
                            px: 1.25,
                          }
                        }}
                      />
                    )}
                  </Stack>
                )}
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Grid>
    );
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', position: 'relative' }}>
      {/* Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h1" fontWeight={700} gutterBottom sx={{ fontSize: '2.5rem' }}>
          Get Started with Unified APIs
        </Typography>
        <Typography variant="h4" color="text.secondary" sx={{ fontSize: '1.25rem', fontWeight: 400 }}>
          {isPredefinedTenant ? (
            <>Your account has been pre-configured with specific API categories</>
          ) : (
            <>Which <strong>API categories</strong> would you like to start with?</>
          )}
        </Typography>
      </Box>

      {/* Predefined Tenant Message */}
      {isPredefinedTenant && predefinedCategories && (
        <Box 
          sx={{ 
            mb: 4, 
            p: 3, 
            backgroundColor: alpha(theme.palette.info.main, 0.08),
            borderRadius: 2,
            border: `1px solid ${alpha(theme.palette.info.main, 0.3)}`,
          }}
        >
          <Stack direction="row" spacing={2} alignItems="flex-start">
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                backgroundColor: alpha(theme.palette.info.main, 0.15),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
              }}
            >
              <Shield size={20} color={theme.palette.info.main} />
            </Box>
            <Box flex={1}>
              <Typography variant="h6" gutterBottom color="info.main">
                Pre-configured Categories
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Your account has been pre-selected with following API categories.
                If you need additional categories, <NAME_EMAIL>
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                {predefinedCategories.map(categoryCode => {
                  const category = CATEGORIES.find(c => c.code === categoryCode);
                  if (!category) return null;
                  return (
                    <Chip
                      key={categoryCode}
                      label={category.name}
                      color="primary"
                      size="small"
                      sx={{ fontWeight: 500 }}
                    />
                  );
                })}
              </Stack>
            </Box>
          </Stack>
        </Box>
      )}

      {/* Category Grid */}
      <Box sx={{ flex: 1, overflowY: 'auto', mb: '80px' }}>
        <Grid container spacing={3}>
          {CATEGORIES.map(category => renderCategoryCard(category))}
        </Grid>
      </Box>

      {/* Floating Bottom Bar */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 0,
          left: '280px', // Account for sidebar width
          right: 0,
          backgroundColor: theme.palette.background.paper,
          borderTop: `1px solid ${theme.palette.divider}`,
          boxShadow: theme.shadows[4],
          zIndex: 100,
        }}
      >
        <Box
          sx={{
            px: 4,
            py: 2,
            display: 'flex',
            justifyContent: 'flex-end',
          }}
        >
          <Stack direction="row" spacing={2}>
            {!isFirstStep && (
              <Button
                variant="outlined"
                onClick={onBack}
                sx={{ minWidth: 120 }}
              >
                Back
              </Button>
            )}
            <Button
              variant="contained"
              onClick={handleNext}
              endIcon={<ChevronRight size={20} />}
              sx={{ minWidth: 120 }}
            >
              {isPredefinedTenant ? 'Continue' : 'Next'}
            </Button>
          </Stack>
        </Box>
      </Box>
    </Box>
  );
}