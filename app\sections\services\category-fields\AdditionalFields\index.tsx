import React, { useState, useEffect } from "react";
import {
   <PERSON>,
   <PERSON>,
   <PERSON><PERSON>ontent,
   <PERSON><PERSON>,
   <PERSON><PERSON><PERSON>,
   Button,
   IconButton,
   Chip,
   Table,
   TableBody,
   TableCell,
   TableContainer,
   TableHead,
   TableRow,
   TextField,
   InputAdornment,
   Dialog,
   DialogTitle,
   DialogContent,
   DialogActions,
   FormControl,
   InputLabel,
   Select,
   MenuItem,
   Tooltip,
   useTheme,
   alpha,
   Collapse,
} from "@mui/material";
import {
   PlusOutlined,
   SearchOutlined,
   EditOutlined,
   DeleteOutlined,
   CloseOutlined,
   SaveOutlined,
   DownOutlined,
   RightOutlined,
} from "@ant-design/icons";
import {
   Braces,
   Brackets,
   Calendar,
   FileText,
   Hash,
   ToggleLeft,
} from "lucide-react";
import { DataModel } from "./type";
import { dataModelsByCategory } from "./data-sets";
import { getDataModelByCategory } from "./utils";
import FieldList from "./field-list";
import useAdditionalAttributes from "../hooks/use-additional-fiels";
import { State } from "hooks/useStatus";
import type { AdditionalFields } from "types/additional-attributes";

interface AdditionalField {
   id: string;
   name: string;
   displayName: string;
   type: string;
   description: string;
   required: boolean;
   defaultValue?: any;
   enum?: string[];
   format?: string;
   validation?: string;
   category: string;
   model: string;
   createdAt: Date;
   createdBy: string;
   usageCount: number;
   lastUsed?: Date;
   parentId?: string;
   children?: AdditionalField[];
}

const mockFields: AdditionalField[] = [
   // Organization fields
   {
      id: "1",
      name: "compliance_tier",
      displayName: "Compliance Tier",
      type: "string",
      description: "Organization compliance tier level",
      required: true,
      enum: ["TIER_1", "TIER_2", "TIER_3"],
      category: "SCM",
      model: "organization",
      createdAt: new Date("2024-01-10"),
      createdBy: "John Doe",
      usageCount: 45,
      lastUsed: new Date("2024-01-20"),
   },
   {
      id: "2",
      name: "team_size",
      displayName: "Team Size",
      type: "number",
      description: "Number of team members in the organization",
      required: false,
      category: "SCM",
      model: "organization",
      createdAt: new Date("2024-01-12"),
      createdBy: "Jane Smith",
      usageCount: 38,
      lastUsed: new Date("2024-01-19"),
   },
   // Repository fields
   {
      id: "3",
      name: "project_owner",
      displayName: "Project Owner",
      type: "string",
      description: "The owner or lead maintainer of the repository",
      required: true,
      category: "SCM",
      model: "repository",
      createdAt: new Date("2024-01-10"),
      createdBy: "John Doe",
      usageCount: 42,
      lastUsed: new Date("2024-01-20"),
   },
   {
      id: "4",
      name: "compliance_level",
      displayName: "Compliance Level",
      type: "string",
      description: "Security compliance level for the repository",
      required: false,
      enum: ["HIGH", "MEDIUM", "LOW"],
      defaultValue: "MEDIUM",
      category: "SCM",
      model: "repository",
      createdAt: new Date("2024-01-12"),
      createdBy: "Jane Smith",
      usageCount: 35,
      lastUsed: new Date("2024-01-19"),
   },
   {
      id: "5",
      name: "last_security_scan",
      displayName: "Last Security Scan",
      type: "date",
      description: "Date of the last security vulnerability scan",
      required: false,
      format: "date-time",
      category: "SCM",
      model: "repository",
      createdAt: new Date("2024-01-08"),
      createdBy: "Security Team",
      usageCount: 22,
      lastUsed: new Date("2024-01-18"),
   },
   // Branch fields
   {
      id: "6",
      name: "protected_by",
      displayName: "Protected By",
      type: "array",
      description: "List of users who can approve changes",
      required: false,
      category: "SCM",
      model: "branch",
      createdAt: new Date("2024-01-05"),
      createdBy: "Admin",
      usageCount: 15,
      lastUsed: new Date("2024-01-15"),
   },
   // Pull Request fields
   {
      id: "7",
      name: "review_priority",
      displayName: "Review Priority",
      type: "string",
      description: "Priority level for code review",
      required: false,
      enum: ["CRITICAL", "HIGH", "NORMAL", "LOW"],
      category: "SCM",
      model: "pull_request",
      createdAt: new Date("2024-01-15"),
      createdBy: "Dev Team",
      usageCount: 28,
      lastUsed: new Date("2024-01-20"),
   },
   {
      id: "8",
      name: "estimated_impact",
      displayName: "Estimated Impact",
      type: "string",
      description: "Estimated impact of the changes",
      required: false,
      category: "SCM",
      model: "pull_request",
      createdAt: new Date("2024-01-16"),
      createdBy: "Dev Team",
      usageCount: 20,
      lastUsed: new Date("2024-01-19"),
   },
   // Example object field with children
   {
      id: "9",
      name: "organization_details",
      displayName: "Organization Details",
      type: "object",
      description: "Detailed organization information",
      required: false,
      category: "SCM",
      model: "organization",
      createdAt: new Date("2024-01-17"),
      createdBy: "Admin",
      usageCount: 15,
      lastUsed: new Date("2024-01-20"),
      children: [
         {
            id: "10",
            name: "address",
            displayName: "Address",
            type: "string",
            description: "Organization address",
            required: false,
            category: "SCM",
            model: "organization",
            parentId: "9",
            createdAt: new Date("2024-01-17"),
            createdBy: "Admin",
            usageCount: 10,
         },
         {
            id: "11",
            name: "contact_info",
            displayName: "Contact Information",
            type: "object",
            description: "Contact details",
            required: false,
            category: "SCM",
            model: "organization",
            parentId: "9",
            createdAt: new Date("2024-01-17"),
            createdBy: "Admin",
            usageCount: 8,
            children: [
               {
                  id: "12",
                  name: "email",
                  displayName: "Email",
                  type: "string",
                  description: "Contact email",
                  required: true,
                  category: "SCM",
                  model: "organization",
                  parentId: "11",
                  createdAt: new Date("2024-01-17"),
                  createdBy: "Admin",
                  usageCount: 5,
               },
            ],
         },
      ],
   },
   // Example array field
   {
      id: "13",
      name: "team_members",
      displayName: "Team Members",
      type: "array",
      description: "List of team members",
      required: false,
      category: "SCM",
      model: "organization",
      createdAt: new Date("2024-01-18"),
      createdBy: "HR",
      usageCount: 25,
      lastUsed: new Date("2024-01-20"),
      children: [],
   },
];

interface AddFieldDialogProps {
   open: boolean;
   onClose: () => void;
   onAdd: (field: Partial<AdditionalField>) => void;
   onEdit?: (field: AdditionalField) => void;
   category: string;
   model?: string;
   dataModels: DataModel[];
   parentField?: AdditionalField;
   editField?: AdditionalField;
   mode?: "add" | "edit";
}

const AddFieldDialog = ({
   open,
   onClose,
   onAdd,
   onEdit,
   category,
   model,
   dataModels,
   parentField,
   editField,
   mode = "add",
}: AddFieldDialogProps) => {
   const theme = useTheme();
   const [fieldData, setFieldData] = useState<Partial<AdditionalField>>({
      name: "",
      displayName: "",
      type: "string",
      description: "",
      required: false,
      category: category,
      model: model || dataModels[0]?.id || "",
      parentId: parentField?.id,
   });

   useEffect(() => {
      if (mode === "edit" && editField) {
         setFieldData({
            ...editField,
            parentId: editField.parentId || parentField?.id,
         });
      } else {
         setFieldData({
            name: "",
            displayName: "",
            type: "string",
            description: "",
            required: false,
            category: category,
            model: model || dataModels[0]?.id || "",
            parentId: parentField?.id,
         });
      }
   }, [mode, editField, category, model, dataModels, parentField]);

   const handleSubmit = () => {
      if (mode === "edit" && editField && onEdit) {
         onEdit({ ...editField, ...fieldData } as AdditionalField);
      } else {
         onAdd(fieldData);
      }
      onClose();
      setFieldData({
         name: "",
         displayName: "",
         type: "string",
         description: "",
         required: false,
         category: category,
         model: model || dataModels[0]?.id || "",
         parentId: parentField?.id,
      });
   };

   return (
      <Dialog
         open={open}
         onClose={onClose}
         maxWidth="sm"
         fullWidth
         PaperProps={{
            variant: "outlined",
            sx: {
               boxShadow: "none",
               border: `1px solid ${theme.palette.mode === "dark" ? "rgba(255, 255, 255, 0.12)" : "#e5e5e5"}`,
            },
         }}
      >
         <DialogTitle>
            <Stack
               direction="row"
               alignItems="center"
               justifyContent="space-between"
            >
               <Typography variant="h6">
                  {mode === "edit"
                     ? "Edit Field"
                     : parentField
                        ? `Add Child Field to ${parentField.displayName}`
                        : "Add Additional Field"}
               </Typography>
               <IconButton onClick={onClose} size="small" sx={{ border: "none" }}>
                  <CloseOutlined />
               </IconButton>
            </Stack>
         </DialogTitle>
         <DialogContent>
            <Stack spacing={3} sx={{ mt: 2 }}>
               {!model && (
                  <FormControl fullWidth>
                     <InputLabel>Data Model</InputLabel>
                     <Select
                        value={fieldData.model}
                        onChange={(e) =>
                           setFieldData({ ...fieldData, model: e.target.value })
                        }
                        label="Data Model"
                     >
                        {dataModels.map((dm) => (
                           <MenuItem key={dm.id} value={dm.id}>
                              {dm.displayName}
                           </MenuItem>
                        ))}
                     </Select>
                  </FormControl>
               )}
               <TextField
                  label="Field Name"
                  value={fieldData.name}
                  onChange={(e) =>
                     setFieldData({ ...fieldData, name: sanitizeFieldName(e.target.value) })
                  }
                  fullWidth
                  helperText="Use lowercase with underscores (e.g., project_owner)"
               />
               {/* <TextField
            label="Display Name"
            value={fieldData.displayName}
            onChange={(e) => setFieldData({ ...fieldData, displayName: e.target.value })}
            fullWidth
            helperText="Human-readable name for the field"
          /> */}
               <FormControl fullWidth>
                  <InputLabel>Field Type</InputLabel>
                  <Select
                     value={fieldData.type}
                     onChange={(e) =>
                        setFieldData({ ...fieldData, type: e.target.value })
                     }
                     label="Field Type"
                  >
                     <MenuItem value="string">
                        <Stack direction="row" spacing={1} alignItems="center">
                           <FileText size={14} color={theme.palette.success.main} />
                           <span>String</span>
                        </Stack>
                     </MenuItem>
                     <MenuItem value="number">
                        <Stack direction="row" spacing={1} alignItems="center">
                           <Hash size={14} color={theme.palette.info.main} />
                           <span>Number</span>
                        </Stack>
                     </MenuItem>
                     <MenuItem value="boolean">
                        <Stack direction="row" spacing={1} alignItems="center">
                           <ToggleLeft size={14} color={theme.palette.warning.main} />
                           <span>Boolean</span>
                        </Stack>
                     </MenuItem>
                     <MenuItem value="date">
                        <Stack direction="row" spacing={1} alignItems="center">
                           <Calendar size={14} color={theme.palette.secondary.main} />
                           <span>Date</span>
                        </Stack>
                     </MenuItem>
                     <MenuItem value="array">
                        <Stack direction="row" spacing={1} alignItems="center">
                           <Brackets size={14} color={theme.palette.error.light} />
                           <span>Array</span>
                        </Stack>
                     </MenuItem>
                     <MenuItem value="object">
                        <Stack direction="row" spacing={1} alignItems="center">
                           <Braces size={14} color={theme.palette.primary.dark} />
                           <span>Object</span>
                        </Stack>
                     </MenuItem>
                  </Select>
               </FormControl>
               <TextField
                  label="Description"
                  value={fieldData.description}
                  onChange={(e) =>
                     setFieldData({ ...fieldData, description: e.target.value })
                  }
                  fullWidth
                  multiline
                  rows={2}
                  helperText="Describe the purpose and usage of this field"
               />
               {/* <FormControlLabel
            control={
              <Switch
                checked={fieldData.required}
                onChange={(e) => setFieldData({ ...fieldData, required: e.target.checked })}
              />
            }
            label="Required Field"
          /> */}
            </Stack>
         </DialogContent>
         <DialogActions>
            <Button onClick={onClose}>Cancel</Button>
            <Button
               variant="contained"
               onClick={handleSubmit}
               disabled={!fieldData.name}
               startIcon={<SaveOutlined />}
            >
               {mode === "edit" ? "Save Changes" : "Add Field"}
            </Button>
         </DialogActions>
      </Dialog>
   );
};

export function sanitizeFieldName(input: string): string {
  return input
    .replace(/\s+/g, "_") // Replace spaces with underscores
    .replace(/[^a-zA-Z0-9_]/g, "") // Allow both cases after first char
    .replace(/^([A-Z])/, (_, first) => first.toLowerCase()); // Force first letter lowercase
}

export default function AdditionalFields({
   category,
   categoryLabel,
}: {
   category: string;
   categoryLabel: string;
}) {
   const [fields, setFields] = useState<any[]>([]);
   const [searchQuery, setSearchQuery] = useState("");
   const [addDialogOpen, setAddDialogOpen] = useState(false);
   const [selectedModel, setSelectedModel] = useState<string | null>(null);
   const [expandedModels, setExpandedModels] = useState<string[]>([]);
   const [parentField, setParentField] = useState<AdditionalField | undefined>(
      undefined
   );
   const [expandedFields, setExpandedFields] = useState<string[]>([]);
   const [editField, setEditField] = useState<AdditionalField | undefined>(
      undefined
   );
   const [dialogMode, setDialogMode] = useState<"add" | "edit">("add");
   const [deleteConfirmDialog, setDeleteConfirmDialog] = useState<{
      open: boolean;
      field?: AdditionalField;
   }>({ open: false });
   const theme = useTheme();

   const { actions, additionalAttributes = [] } = useAdditionalAttributes()

   const dataModels = getDataModelByCategory(category);

   useEffect(() => {
      if (additionalAttributes?.length) {
         const transform = (attributes: AdditionalFields.Root[], parentId: string | null) => {
            const transformedData: Array<AdditionalField> = attributes?.map((i) => {
               return {
                  id: i.id,
                  type: i?.dataType?.type,
                  name: i?.name,
                  displayName: i?.name,
                  description: i?.description,
                  required: false,
                  model: i?.dataModel?.type,
                  createdAt: new Date(i?.changeLog?.createdDateTime),
                  createdBy: 'Current User',
                  usageCount: 0,
                  parentId: '',
                  children: transform(i?.children || [], i.id)
               } as AdditionalField
            })
            return transformedData;
         }
         setFields(transform(additionalAttributes, null))
      }

   }, [additionalAttributes])

   const handleAddField = (fieldData: Partial<AdditionalField>) => {

      const payload = {
         "type": "PREDEFINED",
         "name": fieldData.name as string,
         "key": fieldData.name as string,
         "description": fieldData.description as string,
         "state": State.ACTIVE,
         "category": {
            "type": category
         },
         "dataModel": {
            "type": fieldData.model!
         },
         "dataType": {
            "type": fieldData?.type as string,
         },
         "children": [],
      } as AdditionalFields.CreatePayload

      if (fieldData.parentId) {
         payload.parent = { id: fieldData.parentId }
      }

      actions
         .create({ payload })
         .then(() => {
            console.log('field created successfully')
         })
         .catch(() => {
            console.log('failed to create')
         })
   };

   const handleEditField = (updatedField: AdditionalField) => {
      setFields((prev) =>
         prev.map((field) => {
            if (field.id === updatedField.id) {
               return updatedField;
            }
            // Also check if it's a child field
            if (field.children) {
               return {
                  ...field,
                  children: field.children.map((child: any) =>
                     child.id === updatedField.id ? updatedField : child
                  ),
               };
            }
            return field;
         })
      );
   };

   const handleDeleteField = (fieldToDelete: AdditionalField) => {
      actions.delete(fieldToDelete.id).then(() => {
         setDeleteConfirmDialog({ open: false });
      }).catch(() => console.log('failed to delete'))
   };

   const openEditDialog = (field: AdditionalField) => {
      setEditField(field);
      setDialogMode("edit");
      setSelectedModel(field.model);
      setAddDialogOpen(true);
   };

   const openAddDialog = (model?: string, parent?: AdditionalField) => {
      setEditField(undefined);
      setDialogMode("add");
      setSelectedModel(model || null);
      setParentField(parent);
      setAddDialogOpen(true);
   };

   const toggleModelExpansion = (modelId: string) => {
      setExpandedModels((prev) =>
         prev.includes(modelId)
            ? prev.filter((id) => id !== modelId)
            : [...prev, modelId]
      );
   };

   const toggleFieldExpansion = (fieldId: string) => {
      setExpandedFields((prev) =>
         prev.includes(fieldId)
            ? prev.filter((id) => id !== fieldId)
            : [...prev, fieldId]
      );
   };

   const openAddChildDialog = (field: AdditionalField) => {
      openAddDialog(field.model, field);
   };

   const getFieldsByModel = (modelId: string) => {
      return fields.filter((field) => field.model === modelId);
   };

   const getTypeColor = (type: string) => {
      const colors: Record<string, string> = {
         string: theme.palette.info.main,
         number: theme.palette.success.main,
         boolean: theme.palette.warning.main,
         date: theme.palette.primary.main,
         array: theme.palette.secondary.main,
         object: theme.palette.error.main,
      };
      return colors[type] || theme.palette.grey[500];
   };

   // Recursive component to render field rows with children
   const FieldRow: React.FC<{ field: AdditionalField; level?: number }> = ({
      field,
      level = 0,
   }) => {
      const hasChildren = field.children && field.children.length > 0;
      const canHaveChildren = field.type === "object" || field.type === "array";
      const isExpanded = expandedFields.includes(field.id);

      return (
         <>
            <TableRow
               key={field.id}
               sx={{
                  "&:hover": {
                     bgcolor: alpha(theme.palette.action.hover, 0.04),
                  },
               }}
            >
               <TableCell>
                  <Stack
                     direction="row"
                     spacing={1}
                     alignItems="center"
                     sx={{ pl: level * 4 }}
                  >
                     {canHaveChildren && (
                        <IconButton
                           size="small"
                           onClick={() => toggleFieldExpansion(field.id)}
                           sx={{ p: 0.25 }}
                        >
                           {isExpanded ? <DownOutlined /> : <RightOutlined />}
                        </IconButton>
                     )}
                     <Typography variant="body2" fontFamily="monospace">
                        {field.name}
                     </Typography>
                  </Stack>
               </TableCell>
               <TableCell>
                  <Chip
                     label={field.type}
                     size="small"
                     sx={{
                        bgcolor: alpha(getTypeColor(field.type), 0.1),
                        color: getTypeColor(field.type),
                        fontWeight: 500,
                     }}
                  />
               </TableCell>
               <TableCell>
                  <Tooltip title={field.description}>
                     <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                           maxWidth: 300,
                           overflow: "hidden",
                           textOverflow: "ellipsis",
                           whiteSpace: "nowrap",
                        }}
                     >
                        {field.description}
                     </Typography>
                  </Tooltip>
               </TableCell>
               <TableCell>
                  <Stack direction="row" spacing={1}>
                     {canHaveChildren && (
                        <Tooltip title="Add child field">
                           <IconButton
                              size="small"
                              onClick={() => openAddChildDialog(field)}
                              sx={{
                                 border: "none",
                                 color: "primary.main",
                                 "&:hover": {
                                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                                 },
                              }}
                           >
                              <PlusOutlined />
                           </IconButton>
                        </Tooltip>
                     )}
                     <Tooltip title="Edit field">
                        <IconButton
                           size="small"
                           onClick={() => openEditDialog(field)}
                           sx={{
                              border: "none",
                              "&:hover": {
                                 bgcolor: alpha(theme.palette.primary.main, 0.1),
                                 color: "primary.main",
                              },
                           }}
                        >
                           <EditOutlined />
                        </IconButton>
                     </Tooltip>
                     {/* <Tooltip title="Duplicate field">
                <IconButton
                  size="small"
                  sx={{
                    border: 'none',
                    '&:hover': {
                      bgcolor: alpha(theme.palette.info.main, 0.1),
                      color: 'info.main'
                    }
                  }}
                >
                  <CopyOutlined />
                </IconButton>
              </Tooltip> */}
                     <Tooltip title="Delete field">
                        <IconButton
                           size="small"
                           onClick={() => setDeleteConfirmDialog({ open: true, field })}
                           sx={{
                              border: "none",
                              color: "error.main",
                              "&:hover": {
                                 bgcolor: alpha(theme.palette.error.main, 0.1),
                              },
                           }}
                        >
                           <DeleteOutlined />
                        </IconButton>
                     </Tooltip>
                  </Stack>
               </TableCell>
            </TableRow>
            {isExpanded &&
               hasChildren &&
               field.children?.map((child) => (
                  <FieldRow key={child.id} field={child} level={level + 1} />
               ))}
         </>
      );
   };

   return (
      <Box sx={{ p: 3, height: "100%", overflow: "auto" }}>
         {/* Actions Bar */}
         <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            mb={3}
         >
            <TextField
               placeholder="Search fields..."
               size="small"
               value={searchQuery}
               onChange={(e) => setSearchQuery(e.target.value)}
               sx={{ width: 400 }}
               InputProps={{
                  startAdornment: (
                     <InputAdornment position="start">
                        <SearchOutlined />
                     </InputAdornment>
                  ),
               }}
            />
            <Button
               variant="contained"
               startIcon={<PlusOutlined />}
               onClick={() => openAddDialog()}
            >
               Add Field
            </Button>
         </Stack>

         {/* Data Models with Fields */}
         <Stack spacing={2}>

            {dataModels.map((model) => {
               const modelFields = getFieldsByModel(model.id);
               const isExpanded = expandedModels.includes(model.id);

               return (
                  <Card
                     key={model.id}
                     variant="outlined"
                     sx={{
                        boxShadow: "none",
                        border: `1px solid ${theme.palette.mode === "dark" ? "rgba(255, 255, 255, 0.12)" : "#e5e5e5"}`,
                        transition: "all 0.2s ease",
                        "&:hover": {
                           borderColor: "primary.main",
                           bgcolor: alpha(theme.palette.primary.main, 0.02),
                        },
                     }}
                  >
                     <CardContent
                        sx={{ pb: isExpanded && modelFields.length > 0 ? 1 : 2 }}
                     >
                        {/* Model Header */}
                        <Stack
                           direction="row"
                           alignItems="center"
                           justifyContent="space-between"
                           sx={{
                              cursor: "pointer",
                              py: 0.5,
                           }}
                           onClick={() => toggleModelExpansion(model.id)}
                        >
                           <Stack direction="row" alignItems="center" spacing={2}>
                              <IconButton
                                 size="small"
                                 sx={{
                                    border: "none",
                                    transition: "all 0.2s ease",
                                    "&:hover": {
                                       bgcolor: alpha(theme.palette.action.hover, 0.1),
                                       transform: "scale(1.1)",
                                    },
                                 }}
                              >
                                 {isExpanded ? <DownOutlined /> : <RightOutlined />}
                              </IconButton>
                              <Stack>
                                 <Typography variant="h6" fontWeight={600}>
                                    {model.displayName}
                                 </Typography>
                                 <Typography variant="caption" color="text.secondary">
                                    {model.description}
                                 </Typography>
                              </Stack>
                           </Stack>
                           <Stack direction="row" alignItems="center" spacing={2}>
                              <Chip
                                 label={`${modelFields.length} fields`}
                                 size="small"
                                 color={modelFields.length > 0 ? "primary" : "default"}
                              />
                              <Button
                                 size="small"
                                 variant="text"
                                 startIcon={<PlusOutlined />}
                                 onClick={(e) => {
                                    e.stopPropagation();
                                    openAddDialog(model.id);
                                 }}
                                 sx={{
                                    "&:hover": {
                                       bgcolor: "primary.main",
                                       color: "primary.contrastText",
                                    },
                                 }}
                              >
                                 Add Field
                              </Button>
                           </Stack>
                        </Stack>

                        {/* Model Fields Table */}
                        <Collapse in={isExpanded}>
                           <Box sx={{ mt: 2 }}>
                              <FieldList
                                 modelFields={modelFields}
                                 toggleFieldExpansion={toggleFieldExpansion}
                                 setDeleteConfirmDialog={setDeleteConfirmDialog}
                                 openAddChildDialog={openAddChildDialog}
                                 openEditDialog={openEditDialog}
                                 expandedFields={expandedFields}
                              />
                           </Box>
                        </Collapse>
                     </CardContent>
                  </Card>
               );
            })}
         </Stack>

         {/* Add/Edit Field Dialog */}
         <AddFieldDialog
            open={addDialogOpen}
            onClose={() => {
               setAddDialogOpen(false);
               setSelectedModel(null);
               setParentField(undefined);
               setEditField(undefined);
               setDialogMode("add");
            }}
            onAdd={handleAddField}
            onEdit={handleEditField}
            category={category}
            model={selectedModel || undefined}
            dataModels={dataModels}
            parentField={parentField}
            editField={editField}
            mode={dialogMode}
         />

         {/* Delete Confirmation Dialog */}
         <Dialog
            open={deleteConfirmDialog.open}
            onClose={() => setDeleteConfirmDialog({ open: false })}
            maxWidth="xs"
            fullWidth
         >
            <DialogTitle>Delete Field</DialogTitle>
            <DialogContent>
               <Typography>
                  Are you sure you want to delete the field "
                  {deleteConfirmDialog.field?.name}"?
                  {deleteConfirmDialog.field?.children &&
                     deleteConfirmDialog.field.children.length > 0 &&
                     ` This will also delete ${deleteConfirmDialog.field.children.length} child field(s).`}
               </Typography>
            </DialogContent>
            <DialogActions>
               <Button onClick={() => setDeleteConfirmDialog({ open: false })}>
                  Cancel
               </Button>
               <Button
                  onClick={() =>
                     deleteConfirmDialog.field &&
                     handleDeleteField(deleteConfirmDialog.field)
                  }
                  color="error"
                  variant="contained"
               >
                  Delete
               </Button>
            </DialogActions>
         </Dialog>
      </Box>
   );
}
