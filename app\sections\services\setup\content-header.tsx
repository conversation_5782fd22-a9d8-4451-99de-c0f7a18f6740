import { Stack, Typography } from "@mui/material";
import LearnMoreLink from "components/@extended/LearnMoreLink";
import RefreshIcon from "@mui/icons-material/Refresh";
import IconButton from "components/@extended/IconButton";
import { useServiceProfile } from "hooks/useServiceProfile";
import CategoryFieldsButton from "./CategoryFieldsButton";
import useUserDetails from "store/user";

export default function ContentHeader({
  domain,
  onReload,
  menu,
}: Record<string, any>) {
  const { getDomainName, getDomainUrl } = useServiceProfile();
  const { subscriptions } = useUserDetails();
  const selectedMenuItem = menu.find((item) => item.id === domain);
  const hasSubscription = subscriptions?.some(
    (sub) => sub.productKey === selectedMenuItem?.id
  );
  const isButtonDisabled = selectedMenuItem?.disabled || !hasSubscription;
  return (
    <Stack
      direction={"row"}
      justifyContent={"space-between"}
      alignItems={"center"}
    >
      <Stack gap={1}>
        {/* title */}
        <Typography variant="h5">
          Configure {getDomainName(domain)} integrations
        </Typography>
        <Typography variant="body2" sx={{ color: "text.secondary" }}>
          View supported fields and compare integration coverage.{" "}
          <LearnMoreLink
            href={
              getDomainUrl(domain)
                ? `https://docs.unizo.ai/docs/unified-apis/${getDomainUrl(domain)}/coverage`
                : "https://docs.unizo.ai/getting-started"
            }
            target="_blank"
          >
            Learn more
          </LearnMoreLink>
        </Typography>
      </Stack>

      <Stack direction="row" spacing={1} alignItems="center">
        {domain && domain !== "all" && ["SCM", "TICKETING"].includes(selectedMenuItem?.id) && (
          <CategoryFieldsButton
            category={selectedMenuItem?.id}
            categoryLabel={selectedMenuItem?.title}
            fieldCount={0}
            disabled={isButtonDisabled}
          />
        )}
        <IconButton variant="outlined" onClick={onReload}>
          <RefreshIcon />
        </IconButton>
      </Stack>
    </Stack>
  );
}
