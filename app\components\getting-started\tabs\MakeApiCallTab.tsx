import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  <PERSON>ack,
  Button,
  Paper,
  Grid,
  useTheme,
  alpha,
  IconButton,
  Link,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert,
  TextField,
  Checkbox,
} from '@mui/material';
import { Code2, <PERSON>rk<PERSON>, Key, Copy, Check, AlertCircle, Play } from 'lucide-react';
import TabNavigationFooter from 'components/getting-started/TabNavigationFooter';
import { useAccesskey } from 'hooks/api/useAccesskey';
import platformfetchInstance from 'utils/api/fetchinstance/platform-fetch-Instance';
import fetchInstance from 'utils/api/fetchinstance';
import { State } from 'hooks/useStatus';
import { useGetDockProfile } from 'hooks/api/dockProfiles/useDockProfile';
import useUserDetails from 'store/user';
import { generateUUID } from 'utils/uuid';

const sdkLanguages = [
  {
    name: 'JavaScript/TypeScript',
    shortName: 'TypeScript',
    icon: 'https://unizopublicpaas.blob.core.windows.net/svg/javascript-original.svg',
    description: 'Full TypeScript support, automatic retries, built-in rate limiting',
    docLink: 'https://docs.unizo.ai/docs/sdks/javascript/',
  },
  {
    name: 'Python',
    shortName: 'Python',
    icon: 'https://unizopublicpaas.blob.core.windows.net/svg/python-original.svg',
    description: 'Async/await support, Pydantic models, comprehensive error handling',
    docLink: 'https://docs.unizo.ai/docs/sdks/python',
  },
  {
    name: 'Go',
    shortName: 'Go',
    icon: 'https://unizopublicpaas.blob.core.windows.net/svg/go-original-wordmark.svg',
    description: 'Context support, structured errors, concurrent safe',
    docLink: 'https://docs.unizo.ai/docs/sdks/go',
  },
  {
    name: 'Java',
    shortName: 'Java',
    icon: 'https://unizopublicpaas.blob.core.windows.net/svg/java-original.svg',
    description: 'OkHttp client, Jackson serialization, Spring Boot integration',
    docLink: 'https://docs.unizo.ai/docs/sdks/java',
  },
];

interface DockProfile {
  href: string;
  type: string;
  id: string;
  state: string;
  pageLayout: string;
  name: string;
  displayId: string;
  frontendUrl: string;
  customConfigs: any[];
  tags: any[];
  organization: {
    id: string;
  };
  environment: {
    id: string;
  };
  userFlow: {
    type: string;
  };
  notifications: any[];
  changeLog: {
    createdDateTime: string;
    lastUpdatedDateTime: string;
  };
}

interface DockProfilesResponse {
  pagination: {
    total: number;
    offset: number;
    previous: number;
    next: number;
  };
  data: DockProfile[];
}

interface MakeApiCallTabProps {
  onConnectUIStatusChange?: (enabled: boolean) => void;
  onApiKeyGenerated?: (generated: boolean) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  isFirstTab?: boolean;
  isLastTab?: boolean;
  nextLabel?: string;
  previousLabel?: string;
}

export default function MakeApiCallTab({ 
  onConnectUIStatusChange,
  onApiKeyGenerated,
  onNext,
  onPrevious,
  isFirstTab,
  isLastTab,
  nextLabel,
  previousLabel,
}: MakeApiCallTabProps) {
  const theme = useTheme();
  const [copied, setCopied] = useState(false);
  const { accessKey, generate, createdAt, expiresAt, isPending } = useAccesskey();
  const [apiKeyGenerated, setApiKeyGenerated] = useState(true);
  const [apiKey, setApiKey] = useState('********************************');
  const [dockProfiles, setDockProfiles] = useState<DockProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectUIEnabled, setConnectUIEnabled] = useState(false);
  const [enableError, setEnableError] = useState<string | null>(null);
  const [apiKeyError, setApiKeyError] = useState<string | null>(null);
  
  // New state for integration path configuration
  const [selectedApproach, setSelectedApproach] = useState<'connect-ui' | 'apis' | null>(null);
  const [customDomainEnabled, setCustomDomainEnabled] = useState(false);
  const [customDomain, setCustomDomain] = useState('https://connect.yourapp.com');
  const [scopedAccessEnabled, setScopedAccessEnabled] = useState(false);
  const [isSavingConfiguration, setIsSavingConfiguration] = useState(false);
  const [configurationSaved, setConfigurationSaved] = useState(false);

  const handleGenerateApiKey = async () => {
    try {
      setApiKeyError(null);
      await generate();
      if (onApiKeyGenerated) {
        onApiKeyGenerated(true);
      }
    } catch (error: any) {
      // Handle different error scenarios
      if (error.response?.status === 504) {
        setApiKeyError('Gateway timeout. Please try again in a few moments.');
      } else if (error.response?.status >= 500) {
        setApiKeyError('Server error. Please try again later.');
      } else if (error.response?.status >= 400) {
        setApiKeyError(error.response?.data?.message || 'Failed to generate API key. Please try again.');
      } else if (error.message === 'Network Error') {
        setApiKeyError('Network error. Please check your connection and try again.');
      } else {
        setApiKeyError('An unexpected error occurred. Please try again.');
      }
      console.error('API Key generation error:', error);
    }
  };

  const handleCopyApiKey = () => {
    navigator.clipboard.writeText(accessKey);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Notify parent when API key is available
  useEffect(() => {
    if (accessKey && onApiKeyGenerated) {
      onApiKeyGenerated(true);
      setApiKeyError(null); // Clear error on success
    }
  }, [accessKey, onApiKeyGenerated]);

  // Fetch dock profiles on component mount
  useEffect(() => {
    const fetchDockProfiles = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Get auth token from localStorage or context
        const authToken = localStorage.getItem('authToken') || '';

        const response = await fetchInstance('/dockProfiles', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`,
          },
          params: {
            state: State.ACTIVE
          }
        });

        // if (!response.ok) {
        //   if (response.status === 404) {
        //     // No dock profiles exist yet
        //     setDockProfiles([]);
        //     setConnectUIEnabled(false);
        //     return;
        //   }
        //   throw new Error(`Failed to fetch dock profiles: ${response.status}`);
        // }

        const data: DockProfilesResponse = response?.data;

        const dockProfiles: DockProfilesResponse['data'] = data.data?.filter((i) => i?.state === State.ACTIVE) || [];

        setDockProfiles(dockProfiles);

        // Set toggle state based on whether profiles exist
        const hasProfiles = dockProfiles.length > 0;
        setConnectUIEnabled(hasProfiles);
        if (hasProfiles) {
          setSelectedApproach('connect-ui');
          setConfigurationSaved(true);
        }
        onConnectUIStatusChange?.(hasProfiles);
      } catch (err) {
        console.error('Error fetching dock profiles:', err);
        // Don't show error for 404 as it means no profiles exist yet
        if (err instanceof Error && !err.message.includes('404')) {
          setError(err.message);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchDockProfiles();
  }, [onConnectUIStatusChange]);

  const handleSaveConfiguration = async () => {
    if (!selectedApproach || selectedApproach !== 'connect-ui') return;
    
    setEnableError(null);
    setIsSavingConfiguration(true);

    try {
      const authToken = localStorage.getItem('authToken') || '';
      const frontendUrl = customDomainEnabled ? customDomain : 'https://dock.unizo.ai';

      // Only create if no dock profiles exist
      if (dockProfiles.length === 0) {
        const response = await fetchInstance('/dockProfiles', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`,
          },
          data: {
            type: 'CUSTOM',
            pageLayout: 'EMBEDDED',
            name: 'General',
            userFlow: {
              type: 'CATEGORY'
            },
            frontendUrl: frontendUrl,
            // Add scoped access configuration if needed
          },
        });

        const newDockProfile: DockProfile = response?.data;
        setDockProfiles([newDockProfile]);
      } else if (customDomainEnabled || scopedAccessEnabled) {
        // Update existing profile if custom domain or scoped access changed
        // This would require an UPDATE API endpoint
        // For now, we'll just update the state
      }

      setConnectUIEnabled(true);
      setConfigurationSaved(true);
      onConnectUIStatusChange?.(true);
      setEnableError(null);
    } catch (err) {
      console.error('Error saving configuration:', err);
      let errorMessage = 'Unable to save configuration. Please try again.';
      
      if (err instanceof Error) {
        if (err.message.includes('401')) {
          errorMessage = 'Authentication failed. Please log in again.';
        } else if (err.message.includes('403')) {
          errorMessage = 'You don\'t have permission to enable Connect UI. Please contact your administrator.';
        } else if (err.message.includes('500') || err.message.includes('502') || err.message.includes('503') || err.message.includes('504')) {
          errorMessage = 'Our servers are experiencing issues. Please try again in a few moments.';
        }
      }
      
      setEnableError(errorMessage);
    } finally {
      setIsSavingConfiguration(false);
    }
  };

  // Validate custom domain
  const isValidCustomDomain = (domain: string) => {
    try {
      const url = new URL(domain);
      return url.protocol === 'https:';
    } catch {
      return false;
    }
  };

  const { attemptCreateServiceKey } = useGetDockProfile();
  const { categories } = useUserDetails()

  const onLaunchDemo = () => {
    // window.open('https://demo.unizo.ai/connect', '_blank')
    const payload = {
      "type": "INTEGRATION_TOKEN",
      "name": "Demo Client Session",
      "subOrganization": {
        "name": 'Demo_Consumer',
        "externalKey": generateUUID()
      },
      "integration": {
        "type": "GENERIC",
        "target": {
          "type": "Category",
          categorySelectors: categories?.filter((i) => !i?.disabled)?.map((i) => {
            return { type: i?.value }
          })
        }
      }
    };
    attemptCreateServiceKey(payload, (data: any) => {
      const link = data?.data?.formDescriptorUrl;
      window.open(link, '_blank')
    })
  }

  return (
    <Box>
      <Stack spacing={3}>
        {/* Section 1: API Key */}
        <Box
          sx={{
            backgroundColor: theme.palette.background.default,
            borderRadius: 2,
            p: 4,
            border: `1px solid ${theme.palette.divider}`,
            boxShadow: theme.palette.mode === 'dark' 
              ? `0 1px 3px ${alpha(theme.palette.common.black, 0.3)}` 
              : `0 1px 3px ${alpha(theme.palette.common.black, 0.08)}`,
          }}
        >
          <Stack spacing={4}>
            {/* Header */}
            <Box>
              <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 2 }}>
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: '50%',
                    backgroundColor: theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: theme.palette.common.white,
                  }}
                >
                  <Key size={18} strokeWidth={3} />
                </Box>
                <Typography variant="h5" fontWeight={600}>
                  API Key
                </Typography>
              </Stack>
              <Typography
                variant="body1"
                sx={{
                  color: theme.palette.mode === 'light'
                    ? theme.palette.grey[600]
                    : theme.palette.text.secondary,
                  maxWidth: 800,
                }}
              >
                Authenticate your product with Unizo's APIs. You'll include this key in server side requests.
              </Typography>
            </Box>

            {/* API Key Card */}
            <Box sx={{ maxWidth: 800 }}>
              <Box
                sx={{
                  backgroundColor: theme.palette.background.paper,
                  borderRadius: 2,
                  p: 4,
                }}
              >
                {/* API Key Generation */}
                {!accessKey ? (
                  <Box>
                    <Typography
                      variant="body2"
                      sx={{
                        mb: 1.5,
                        color: theme.palette.mode === 'light'
                          ? theme.palette.grey[700]
                          : theme.palette.text.primary,
                        fontWeight: 500,
                      }}
                    >
                      You need an API key to call REST APIs or use our SDKs. If you haven't generated one yet, generate it here:
                    </Typography>
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<Key size={16} />}
                      onClick={handleGenerateApiKey}
                      disabled={isPending}
                      sx={{
                        textTransform: 'none',
                        fontWeight: 500,
                        px: 2,
                        py: 1,
                      }}
                    >
                      {isPending ? 'Generating...' : 'Generate API Key'}
                    </Button>
                    {apiKeyError && (
                      <Alert 
                        severity="error" 
                        sx={{ 
                          mt: 2,
                          '& .MuiAlert-icon': {
                            fontSize: '20px',
                          },
                        }}
                        onClose={() => setApiKeyError(null)}
                      >
                        {apiKeyError}
                      </Alert>
                    )}
                  </Box>
                ) : (
                  <Box>
                    <Box
                      sx={{
                        p: 2,
                        backgroundColor: alpha(theme.palette.warning.main, 0.08),
                        borderRadius: 1,
                        border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`,
                        mb: 2,
                      }}
                    >
                      <Typography
                        variant="caption"
                        sx={{
                          color: theme.palette.warning.dark,
                          fontWeight: 500,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5,
                        }}
                      >
                        <Sparkles size={14} />
                        Keep this API key safe. It will not be shown again.
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        p: 1.5,
                        backgroundColor: theme.palette.mode === 'light'
                          ? theme.palette.grey[50]
                          : alpha(theme.palette.grey[800], 0.5),
                        borderRadius: 1,
                        border: `1px solid ${theme.palette.divider}`,
                        mb: 2,
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          fontFamily: 'monospace',
                          flex: 1,
                          color: theme.palette.text.primary,
                        }}
                      >
                        {accessKey}
                      </Typography>
                      <IconButton
                        size="small"
                        onClick={handleCopyApiKey}
                        sx={{
                          color: copied ? theme.palette.success.main : theme.palette.text.secondary,
                        }}
                      >
                        {copied ? <Check size={16} /> : <Copy size={16} />}
                      </IconButton>
                    </Box>
                    <Stack direction="row" spacing={4}>
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Created
                        </Typography>
                        <Typography variant="body2">{createdAt ?? '-'}</Typography>
                      </Box>
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Expires
                        </Typography>
                        <Typography variant="body2">{expiresAt ?? '-'}</Typography>
                      </Box>
                    </Stack>
                  </Box>
                )}
              </Box>
            </Box>
          </Stack>
        </Box>

        {/* Section 2: Integration Path */}
        <Box
          sx={{
            backgroundColor: theme.palette.background.default,
            borderRadius: 2,
            p: 4,
            border: `1px solid ${theme.palette.divider}`,
            boxShadow: theme.palette.mode === 'dark' 
              ? `0 1px 3px ${alpha(theme.palette.common.black, 0.3)}` 
              : `0 1px 3px ${alpha(theme.palette.common.black, 0.08)}`,
          }}
        >
          <Stack spacing={4}>
            {/* Header */}
            <Box>
              <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 2 }}>
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: '50%',
                    backgroundColor: theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: theme.palette.common.white,
                  }}
                >
                  <Code2 size={18} strokeWidth={3} />
                </Box>
                <Typography variant="h5" fontWeight={600}>
                  How Users Link and Authorize Their Tools
                </Typography>
              </Stack>
              <Typography
                variant="body1"
                sx={{
                  color: theme.palette.mode === 'light'
                    ? theme.palette.grey[600]
                    : theme.palette.text.secondary,
                  maxWidth: 800,
                }}
              >
                Guide how your app handles authentication when users connect tools.
              </Typography>

            </Box>

            {/* Integration Configuration Card */}
            <Box sx={{ maxWidth: 800 }}>
              <Box
                sx={{
                  backgroundColor: theme.palette.background.paper,
                  borderRadius: 2,
                  p: 3,
                }}
              >
                {/* Step 1 - Choose your approach */}
                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant="body1"
                    sx={{
                      fontWeight: 600,
                      mb: 2,
                      color: theme.palette.text.primary,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                    }}
                  >
                    Choose the link & auth experience: Connect UI (fully managed) or API-only (DIY with Unizo APIs)
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: theme.palette.text.secondary,
                      mb: 2,
                    }}
                  >
                    Use Connect UI (embedded, fully managed) for the fastest launch, or build a custom UI using Unizo's APIs for maximum flexibility.
                  </Typography>
                  
                  {/* Option Cards */}
                  <Stack spacing={2}>
                    {/* Enable Connect UI Option */}
                    <Box
                      sx={{
                        p: 2,
                        border: `2px solid ${selectedApproach === 'connect-ui' ? theme.palette.primary.main : theme.palette.divider}`,
                        borderRadius: 1.5,
                        backgroundColor: selectedApproach === 'connect-ui'
                          ? alpha(theme.palette.primary.main, 0.04)
                          : theme.palette.background.default,
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          borderColor: theme.palette.primary.main,
                          backgroundColor: alpha(theme.palette.primary.main, 0.04),
                        },
                      }}
                      onClick={() => {
                        setSelectedApproach('connect-ui');
                        setConfigurationSaved(false);
                      }}
                    >
                      <Stack direction="row" spacing={1.5} alignItems="flex-start">
                        <Box
                          sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '50%',
                            border: `2px solid ${selectedApproach === 'connect-ui' ? theme.palette.primary.main : theme.palette.divider}`,
                            backgroundColor: selectedApproach === 'connect-ui' ? theme.palette.primary.main : 'transparent',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mt: 0.5,
                            flexShrink: 0,
                          }}
                        >
                          {selectedApproach === 'connect-ui' && (
                            <Box
                              sx={{
                                width: 8,
                                height: 8,
                                borderRadius: '50%',
                                backgroundColor: theme.palette.common.white,
                              }}
                            />
                          )}
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Typography variant="body2" fontWeight={600}>
                              Enable Connect UI
                            </Typography>
                            <Box
                              sx={{
                                px: 1,
                                py: 0.25,
                                backgroundColor: alpha(theme.palette.success.main, 0.1),
                                color: theme.palette.success.main,
                                borderRadius: 1,
                                fontSize: '0.75rem',
                                fontWeight: 600,
                              }}
                            >
                              Recommended
                            </Box>
                          </Box>
                          <Typography variant="caption" color="text.secondary">
                            Managed OAuth/PAT flows, secure token storage, error handling.
                          </Typography>
                        </Box>
                      </Stack>
                    </Box>

                    {/* Use APIs Option */}
                    <Box
                      component="button"
                      type="button"
                      sx={{
                        width: '100%',
                        p: 2.5,
                        border: `1px solid`,
                        borderColor: selectedApproach === 'apis' 
                          ? theme.palette.primary.main 
                          : theme.palette.divider,
                        borderRadius: 2,
                        backgroundColor: selectedApproach === 'apis'
                          ? alpha(theme.palette.primary.main, 0.08)
                          : theme.palette.background.paper,
                        cursor: 'pointer',
                        transition: theme.transitions.create(['border-color', 'background-color', 'box-shadow'], {
                          duration: theme.transitions.duration.short,
                        }),
                        display: 'block',
                        textAlign: 'left',
                        position: 'relative',
                        overflow: 'hidden',
                        '&:hover': {
                          borderColor: selectedApproach === 'apis' 
                            ? theme.palette.primary.main 
                            : theme.palette.action.hover,
                          backgroundColor: selectedApproach === 'apis'
                            ? alpha(theme.palette.primary.main, 0.08)
                            : theme.palette.action.hover,
                        },
                        '&:focus': {
                          outline: 'none',
                          boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.2)}`,
                        },
                        '&:focus-visible': {
                          outline: `2px solid ${theme.palette.primary.main}`,
                          outlineOffset: 2,
                        },
                      }}
                      onClick={() => {
                        setSelectedApproach('apis');
                        setConfigurationSaved(false);
                      }}
                      aria-pressed={selectedApproach === 'apis'}
                      aria-label="Use APIs to build your own UI"
                    >
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                        {/* Radio Button Indicator */}
                        <Box
                          sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '50%',
                            border: `2px solid`,
                            borderColor: selectedApproach === 'apis' 
                              ? theme.palette.primary.main 
                              : theme.palette.action.disabled,
                            backgroundColor: selectedApproach === 'apis' 
                              ? theme.palette.primary.main 
                              : 'transparent',
                            mt: 0.25,
                            flexShrink: 0,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            transition: theme.transitions.create(['border-color', 'background-color']),
                          }}
                        >
                          {selectedApproach === 'apis' && (
                            <Box
                              sx={{
                                width: 8,
                                height: 8,
                                borderRadius: '50%',
                                backgroundColor: theme.palette.primary.contrastText,
                              }}
                            />
                          )}
                        </Box>
                        
                        {/* Text Content */}
                        <Box sx={{ flex: 1, minWidth: 0 }}>
                          <Typography 
                            variant="body2" 
                            component="div"
                            sx={{ 
                              fontWeight: 600,
                              color: theme.palette.text.primary,
                              mb: 0.5,
                            }}
                          >
                            Use APIs (build your own UI)
                          </Typography>
                          <Typography 
                            variant="caption" 
                            component="div"
                            sx={{
                              color: theme.palette.text.secondary,
                              lineHeight: 1.5,
                            }}
                          >
                            Full control over UX; call Unizo to initiate/complete auth.
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Stack>
                </Box>

                {/* Conditional Steps 2 & 3 - Only show when Connect UI is selected */}
                {selectedApproach === 'connect-ui' && (
                  <>
                    {/* Step 2 - Change Connect UI's URL using Custom Domains */}
                    <Box sx={{ mb: 4 }}>
                      <Typography
                        variant="body1"
                        sx={{
                          fontWeight: 600,
                          mb: 2,
                          color: theme.palette.text.primary,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                        }}
                      >
                        Change Connect UI's URL using Custom Domains
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: theme.palette.text.secondary,
                          mb: 2,
                        }}
                      >
                        By default, Connect UI loads from <code style={{ 
                          backgroundColor: alpha(theme.palette.primary.main, 0.1),
                          padding: '2px 6px',
                          borderRadius: '4px',
                          fontSize: '0.875rem',
                        }}>https://dock.unizo.ai</code> in your product's web UI (rendered in your user's browser).
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: theme.palette.text.secondary,
                          mb: 2,
                        }}
                      >
                        You can optionally use a <strong>custom domain</strong> so the URL matches your application. This requires a small network/DNS configuration on your web UI's domain.
                      </Typography>

                      {/* Custom domain checkbox */}
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={customDomainEnabled}
                            onChange={(e) => {
                              setCustomDomainEnabled(e.target.checked);
                              setConfigurationSaved(false);
                            }}
                            sx={{
                              '&.Mui-checked': {
                                color: theme.palette.primary.main,
                              },
                            }}
                          />
                        }
                        label={
                          <Typography variant="body2" fontWeight={500}>
                            Enable custom domain
                          </Typography>
                        }
                      />
                      
                      {/* Custom domain input field */}
                      {customDomainEnabled ? (
                        <TextField
                          fullWidth
                          size="small"
                          value={customDomain}
                          onChange={(e) => {
                            setCustomDomain(e.target.value);
                            setConfigurationSaved(false);
                          }}
                          error={customDomainEnabled && !isValidCustomDomain(customDomain)}
                          helperText={
                            customDomainEnabled && !isValidCustomDomain(customDomain)
                              ? 'Please enter a valid HTTPS URL'
                              : ''
                          }
                          sx={{
                            mt: 1.5,
                            '& .MuiOutlinedInput-root': {
                              fontFamily: 'monospace',
                            },
                          }}
                        />
                      ) : (
                        <Box
                          sx={{
                            mt: 1.5,
                            p: 1.5,
                            backgroundColor: theme.palette.background.default,
                            border: `1px solid ${theme.palette.divider}`,
                            borderRadius: 1,
                            opacity: 0.6,
                          }}
                        >
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: 'monospace',
                              color: theme.palette.text.secondary,
                            }}
                          >
                            https://connect.yourapp.com
                          </Typography>
                        </Box>
                      )}
                    </Box>

                    {/* Step 3 - Enable scoped access */}
                    <Box>
                      <Typography
                        variant="body1"
                        sx={{
                          fontWeight: 600,
                          mb: 2,
                          color: theme.palette.text.primary,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                        }}
                      >
                        Enable scoped access (optional)
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: theme.palette.text.secondary,
                          mb: 2,
                        }}
                      >
                        Scoped access lets your customers choose specific resources (repos, projects, org units, etc.) for each new integration, adding an extra layer of security and least-privilege control.
                      </Typography>

                      {/* Scoped access checkbox */}
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={scopedAccessEnabled}
                            onChange={(e) => {
                              setScopedAccessEnabled(e.target.checked);
                              setConfigurationSaved(false);
                            }}
                            sx={{
                              '&.Mui-checked': {
                                color: theme.palette.primary.main,
                              },
                            }}
                          />
                        }
                        label={
                          <Typography variant="body2" fontWeight={500}>
                            Enable scoped access in Connect UI
                          </Typography>
                        }
                      />
                    </Box>

                    {/* Save Configuration Button */}
                    <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-start', gap: 2 }}>
                      <Button
                        variant="contained"
                        size="small"
                        onClick={handleSaveConfiguration}
                        disabled={
                          isSavingConfiguration || 
                          configurationSaved || 
                          (customDomainEnabled && !isValidCustomDomain(customDomain))
                        }
                        sx={{
                          textTransform: 'none',
                          fontWeight: 500,
                          px: 3,
                          backgroundColor: theme.palette.grey[900],
                          color: theme.palette.common.white,
                          '&:hover': {
                            backgroundColor: theme.palette.grey[800],
                          },
                          '&:disabled': {
                            backgroundColor: theme.palette.action.disabledBackground,
                            color: theme.palette.text.primary,
                            opacity: 1,
                          },
                        }}
                      >
                        {isSavingConfiguration ? (
                          <>
                            <CircularProgress size={16} sx={{ mr: 1 }} color="inherit" />
                            Saving...
                          </>
                        ) : configurationSaved ? (
                          <>
                            <Check size={16} style={{ marginRight: 6 }} />
                            Saved
                          </>
                        ) : (
                          'Save'
                        )}
                      </Button>
                      
                      {/* Try Test Run Button - Show after configuration is saved */}
                      {configurationSaved && (
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<Play size={16} />}
                          onClick={onLaunchDemo}
                          sx={{
                            textTransform: 'none',
                            fontWeight: 500,
                            borderColor: theme.palette.divider,
                            color: theme.palette.text.primary,
                            '&:hover': {
                              borderColor: theme.palette.primary.main,
                              backgroundColor: alpha(theme.palette.primary.main, 0.04),
                            },
                          }}
                        >
                          Try Test Run
                        </Button>
                      )}
                    </Box>
                  </>
                )}
              </Box>
            </Box>
          </Stack>
        </Box>
      </Stack>
      
      <TabNavigationFooter
        onNext={onNext}
        onPrevious={onPrevious}
        isFirstTab={isFirstTab}
        isLastTab={isLastTab}
        nextLabel={nextLabel}
        previousLabel={previousLabel}
      />
    </Box>
  );
}